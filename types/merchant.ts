// Merchant System Types

export interface ProductCategory {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  slug: string;
  color: string;
  icon?: string;
  isActive: boolean;
  productCount: number;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Merchant {
  id: string;
  name: string;
  email: string;
  phone: string;
  logo: string;
  rating: number;
  totalProducts: number;
  totalSales: number;
  joinedAt: Date;
  status: "active" | "pending" | "suspended";
  businessDetails: {
    businessName: string;
    businessAddress: string;
    taxId: string;
    businessType: string;
  };
}

export interface GlobalManufacturingPhase {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  estimatedDuration: number; // in days
  order: number;
  isRequired: boolean;
  isGlobal: true;
  createdAt: Date;
  updatedAt: Date;
}

export interface ManufacturingPhase {
  id: string;
  productId?: string; // Optional for global phases
  merchantId: string;
  name: string;
  description: string;
  estimatedDuration: number; // in days
  order: number;
  isRequired: boolean;
  isGlobal: boolean; // true for global phases, false for product-specific
  globalPhaseId?: string; // Reference to global phase if this is derived from one
  createdAt: Date;
  updatedAt: Date;
}

export interface ManufacturingUpdate {
  id: string;
  groupId: string;
  phaseId: string;
  merchantId: string;
  title: string;
  description: string;
  images: string[];
  videos?: string[];
  status: "in_progress" | "completed" | "delayed" | "on_hold";
  completionPercentage: number;
  estimatedCompletion?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupPurchase {
  id: string;
  productId: string;
  merchantId: string;
  groupName: string;
  adminId: string;
  totalMembers: number;
  targetAmount: number;
  currentAmount: number;
  paymentProgress: number; // percentage
  status:
    | "discussing"
    | "collecting"
    | "manufacturing"
    | "installing"
    | "completed"
    | "ready_for_installation";
  createdAt: Date;
  targetDate?: Date;
  manufacturingStarted?: Date;
  manufacturingCompleted?: Date;
  productImage?: string;
  productName?: string;
  estimatedCompletion?: Date;
}

export interface TeamMember {
  id: string;
  merchantId: string;
  name: string;
  email: string;
  phone: string;
  photoUrl?: string;
  role:
    | "contractor"
    | "employee"
    | "manager"
    | "factory_worker"
    | "factory_manager";
  specialties: string[];
  isActive: boolean;
  isVerified?: boolean;
  bankingDetails?: {
    accountNumber: string;
    routingNumber: string;
    bankName: string;
    accountHolderName: string;
  };
  identification?: {
    idType: "drivers_license" | "passport" | "national_id";
    idNumber: string;
    idImageUrl: string;
  };
  joinedAt: Date;
}

export interface InstallationTask {
  id: string;
  groupId: string;
  productId: string;
  merchantId: string;
  title: string;
  description: string;
  installationAddress: string;
  scheduledDate: Date;
  estimatedDuration: number; // in hours
  assignedTeam: string[]; // TeamMember IDs
  teamLeadId: string;
  status:
    | "scheduled"
    | "in_progress"
    | "completed"
    | "cancelled"
    | "pending_approval";
  completionImages?: string[];
  completionNotes?: string;
  customerApproval?: {
    approved: boolean;
    approvedBy: string;
    approvedAt: Date;
    feedback?: string;
  };
  paymentReleased: boolean;
  createdAt: Date;
  updatedAt: Date;
  productImage?: string;
  productName?: string;
  groupName?: string;
}

export interface PaymentTracking {
  groupId: string;
  productId: string;
  merchantId: string;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  paymentProgress: number;
  memberPayments: {
    userId: string;
    userName: string;
    shareAmount: number;
    paidAmount: number;
    paymentStatus: "pending" | "partial" | "completed";
    lastPaymentDate?: Date;
  }[];
  readyForManufacturing: boolean; // 80% threshold
  manufacturingThreshold: number; // default 80%
}

export interface ProductMaterialRequirement {
  materialId: string;
  materialName: string;
  requiredQuantity: number;
  unit: string;
  isOptional: boolean;
  notes?: string;
}

export interface MerchantInventoryItem {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  specifications: Record<string, string>;
  manufacturingTime: number; // in days
  manufacturingPhases: ManufacturingPhase[];
  materialRequirements: ProductMaterialRequirement[]; // Required materials for this product
  isActive: boolean;
  stockStatus: "in_stock" | "made_to_order" | "out_of_stock";
  createdAt: Date;
  updatedAt: Date;
}

export interface UpsellCategory {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  slug: string;
  color: string;
  icon?: string;
  isActive: boolean;
  itemCount: number;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface UpsellItem {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  specifications: Record<string, string>;
  isActive: boolean;
  stockStatus: "in_stock" | "made_to_order" | "out_of_stock";
  applicableProducts: string[]; // Product IDs this upsell applies to
  upsellType: "accessory" | "upgrade" | "service" | "warranty";
  priority: number; // Display priority (1 = highest)
  createdAt: Date;
  updatedAt: Date;
}

export interface RawMaterialSupplier {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  rating: number;
  isActive: boolean;
  paymentTerms: string;
  deliveryTime: number; // in days
  createdAt: Date;
  updatedAt: Date;
}

export interface RawMaterial {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  category: string;
  unit: string; // kg, m², pieces, etc.
  costPerUnit: number;
  currentStock: number;
  minimumStock: number;
  maximumStock: number;
  supplierId: string;
  supplier?: RawMaterialSupplier;
  specifications: Record<string, string>;
  storageLocation: string;
  isActive: boolean;
  lastRestocked: Date;
  expiryDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface StockTransaction {
  id: string;
  materialId: string;
  merchantId: string;
  type: "order" | "receive" | "usage" | "adjustment" | "return";
  quantity: number;
  unitCost?: number; // For orders and receipts
  totalCost?: number; // For orders and receipts
  balanceAfter: number;
  reference?: string; // Order number, job reference, etc.
  notes?: string;
  supplierId?: string; // For orders and receipts
  supplier?: RawMaterialSupplier;
  orderId?: string; // Link to purchase order
  usageReason?: string; // Manufacturing, waste, etc.
  createdBy: string; // User who made the transaction
  createdAt: Date;
  updatedAt: Date;
}

export interface PurchaseOrder {
  id: string;
  merchantId: string;
  supplierId: string;
  supplier?: RawMaterialSupplier;
  orderNumber: string;
  status:
    | "draft"
    | "sent"
    | "confirmed"
    | "partially_received"
    | "completed"
    | "cancelled";
  orderDate: Date;
  expectedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  items: PurchaseOrderItem[];
  subtotal: number;
  tax: number;
  total: number;
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PurchaseOrderItem {
  id: string;
  materialId: string;
  material?: RawMaterial;
  quantity: number;
  unitCost: number;
  totalCost: number;
  receivedQuantity: number;
  notes?: string;
}

export interface StockUsage {
  id: string;
  materialId: string;
  material?: RawMaterial;
  quantity: number;
  usageType: "manufacturing" | "waste" | "sample" | "maintenance" | "other";
  reference?: string; // Job number, product ID, etc.
  description: string;
  usedBy: string;
  usedAt: Date;
  createdAt: Date;
}

export interface JobCard {
  id: string;
  jobNumber: string;
  groupId: string;
  productId: string;
  merchantId: string;
  groupName: string;
  productName: string;
  productImage?: string;
  status: "pending" | "in_progress" | "completed" | "on_hold" | "cancelled";
  priority: "low" | "medium" | "high" | "urgent";
  assignedTeam: string[]; // TeamMember IDs
  teamLeadId?: string;
  estimatedStartDate: Date;
  actualStartDate?: Date;
  estimatedCompletionDate: Date;
  actualCompletionDate?: Date;
  phases: JobCardPhase[];
  materialRequirements: JobCardMaterial[];
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface JobCardPhase {
  id: string;
  phaseId: string; // Reference to ManufacturingPhase
  phaseName: string;
  description: string;
  estimatedDuration: number;
  actualDuration?: number;
  status: "pending" | "in_progress" | "completed" | "skipped";
  assignedWorkers: string[]; // TeamMember IDs
  startDate?: Date;
  completionDate?: Date;
  notes?: string;
  order: number;
}

export interface JobCardMaterial {
  id: string;
  materialId: string;
  materialName: string;
  requiredQuantity: number;
  usedQuantity: number;
  unit: string;
  costPerUnit: number;
  totalCost: number;
  status: "pending" | "allocated" | "used" | "returned";
  allocatedBy?: string;
  allocatedAt?: Date;
  usedBy?: string;
  usedAt?: Date;
}

// Dashboard Statistics
export interface MerchantDashboardStats {
  totalGroups: number;
  activeGroups: number;
  readyForManufacturing: number;
  inManufacturing: number;
  pendingInstallation: number;
  completedInstallations: number;
  totalRevenue: number;
  monthlyRevenue: number;
  averageGroupSize: number;
  averageOrderValue: number;
}

// Filter and Search Types
export interface GroupFilter {
  status?: GroupPurchase["status"];
  paymentProgress?: "below_50" | "50_to_80" | "above_80" | "completed";
  dateRange?: {
    start: Date;
    end: Date;
  };
  productCategory?: string;
  sortBy?: "created_date" | "payment_progress" | "target_amount" | "group_size";
  sortOrder?: "asc" | "desc";
}

export interface InstallationFilter {
  status?: InstallationTask["status"];
  dateRange?: {
    start: Date;
    end: Date;
  };
  teamMember?: string;
  sortBy?: "scheduled_date" | "created_date" | "status";
  sortOrder?: "asc" | "desc";
}
