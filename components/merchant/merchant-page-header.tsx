"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { ReactNode } from "react";

interface MerchantPageHeaderProps {
  title: string;
  description?: string;
  showBackButton?: boolean;
  backHref?: string;
  actions?: ReactNode;
  children?: ReactNode; // For custom content like avatars, badges, etc.
}

export function MerchantPageHeader({
  title,
  description,
  showBackButton = false,
  backHref,
  actions,
  children,
}: MerchantPageHeaderProps) {
  const pathname = usePathname();

  // Auto-determine back href based on current path if not provided
  const getBackHref = () => {
    if (backHref) return backHref;
    
    const pathSegments = pathname.split('/').filter(Boolean);
    
    // Remove the last segment to go back one level
    if (pathSegments.length > 2) {
      pathSegments.pop();
      return '/' + pathSegments.join('/');
    }
    
    // Default fallback
    return '/merchant';
  };

  // Auto-determine if back button should be shown based on path depth
  const shouldShowBackButton = showBackButton || (() => {
    const pathSegments = pathname.split('/').filter(Boolean);
    // Show back button if we're deeper than /merchant/[section]
    return pathSegments.length > 2;
  })();

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        {shouldShowBackButton && (
          <Link href={getBackHref()}>
            <Button variant="outline" size="icon">
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </Link>
        )}
        
        <div className="flex items-center space-x-4">
          {children}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            {description && (
              <p className="text-muted-foreground">{description}</p>
            )}
          </div>
        </div>
      </div>

      {actions && (
        <div className="flex items-center space-x-2">
          {actions}
        </div>
      )}
    </div>
  );
}
