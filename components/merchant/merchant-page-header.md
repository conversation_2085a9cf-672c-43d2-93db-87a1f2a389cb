# MerchantPageHeader Component

A reusable header component for merchant pages that automatically handles back navigation, titles, descriptions, and action buttons.

## Features

- **Automatic back button detection**: Shows back button based on URL depth
- **Smart back navigation**: Auto-generates back href based on current path
- **Flexible actions**: Support for action buttons on the right
- **Custom content**: Support for additional content like avatars, badges, etc.
- **Consistent styling**: Follows the established merchant page design patterns

## Usage Examples

### 1. Simple Main Page (No Back Button)

```tsx
<MerchantPageHeader
  title="Inventory"
  description="Manage your product catalog and manufacturing settings"
/>
```

### 2. Main Page with Actions

```tsx
<MerchantPageHeader
  title="Inventory"
  description="Manage your product catalog and manufacturing settings"
  actions={
    <Link href="/merchant/inventory/new">
      <Button>
        <Plus className="mr-2 h-4 w-4" />
        Add Product
      </Button>
    </Link>
  }
/>
```

### 3. Detail Page with Auto Back Button

```tsx
<MerchantPageHeader
  title={product.name}
  description="Product details and settings"
  // backHref="/merchant/inventory" // Optional - auto-detected
/>
```

### 4. Form Page with Custom Back Link

```tsx
<MerchantPageHeader
  title="Add New Product"
  description="Create a new product for your inventory catalog"
  backHref="/merchant/inventory"
/>
```

### 5. Page with Custom Content (Avatar, Badges, etc.)

```tsx
<MerchantPageHeader
  title={memberData.userName}
  description="Payment history and group memberships"
  backHref="/merchant/payments"
>
  <Avatar className="h-12 w-12">
    <AvatarImage src={memberData.avatar} alt={memberData.userName} />
    <AvatarFallback>
      {memberData.userName.split(" ").map((n) => n[0]).join("")}
    </AvatarFallback>
  </Avatar>
</MerchantPageHeader>
```

### 6. Page with Dynamic Actions

```tsx
<MerchantPageHeader
  title={product.name}
  description="Product details and settings"
  actions={
    <div className="flex space-x-2">
      {isEditing ? (
        <>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        </>
      ) : (
        <>
          <Link href={`/merchant/inventory/${productId}/manufacturing`}>
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              Manufacturing Phases
            </Button>
          </Link>
          <Button onClick={() => setIsEditing(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Product
          </Button>
        </>
      )}
    </div>
  }
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | Required | The main heading text |
| `description` | `string` | Optional | Subtitle/description text |
| `showBackButton` | `boolean` | Auto-detected | Whether to show the back button |
| `backHref` | `string` | Auto-generated | Custom back navigation URL |
| `actions` | `ReactNode` | Optional | Action buttons/content for the right side |
| `children` | `ReactNode` | Optional | Custom content (avatars, badges, etc.) |

## Auto-Detection Logic

- **Back Button**: Automatically shown if URL depth > 2 (e.g., `/merchant/inventory/new`)
- **Back Href**: Auto-generated by removing the last path segment
- **Override**: Use `showBackButton={false}` or custom `backHref` to override defaults

## Migration from Old Headers

Replace this pattern:
```tsx
<div className="flex items-center justify-between">
  <div className="flex items-center space-x-4">
    <Link href="/merchant/inventory">
      <Button variant="outline" size="icon">
        <ChevronLeft className="h-4 w-4" />
      </Button>
    </Link>
    <div>
      <h1 className="text-3xl font-bold tracking-tight">Add New Product</h1>
      <p className="text-muted-foreground">
        Create a new product for your inventory catalog
      </p>
    </div>
  </div>
</div>
```

With this:
```tsx
<MerchantPageHeader
  title="Add New Product"
  description="Create a new product for your inventory catalog"
  backHref="/merchant/inventory"
/>
```
