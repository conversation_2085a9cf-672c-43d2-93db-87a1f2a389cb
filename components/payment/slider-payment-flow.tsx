"use client";

import { useState, useEffect } from "react";
import styles from "./slider-payment-flow.module.css";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Product } from "@/data/products";
import { LaybyTerm } from "@/types/payment";
import { formatPrice } from "@/lib/utils";
import {
  User,
  Users,
  CreditCard,
  Calendar,
  Calculator,
  Mail,
  Phone,
  Plus,
  UserPlus,
} from "lucide-react";

interface SliderPaymentFlowProps {
  product: Product;
  quantity: number;
}

interface PaymentFlowData {
  contributorCount: number;
  paymentMode: "solo-full" | "solo-layby" | "group";
  termMonths?: number;
  monthlyAmount: number;
  totalAmount: number;
  invitedMembers: string[];
}

interface InvitedMember {
  contact: string;
  type: "email" | "phone";
}

export function SliderPaymentFlow({
  product,
  quantity,
}: SliderPaymentFlowProps) {
  const [contributorCount, setContributorCount] = useState(1);
  const [paymentMode, setPaymentMode] = useState<
    "solo-full" | "solo-layby" | "group"
  >("solo-layby");
  const [termMonths, setTermMonths] = useState<LaybyTerm>(12);
  const [invitedMembers, setInvitedMembers] = useState<InvitedMember[]>([]);
  const [newMemberContact, setNewMemberContact] = useState("");
  const [contactType, setContactType] = useState<"email" | "phone">("email");

  const totalPrice = product.price * quantity;

  // Calculate interest rate based on term
  const getInterestRate = (months: number): number => {
    const rates: Record<LaybyTerm, number> = {
      3: 5,
      6: 8,
      9: 10,
      12: 12,
      18: 15,
      24: 18,
      36: 22,
    };
    return rates[months as LaybyTerm] || 12;
  };

  // Calculate payment amounts
  const calculatePayments = () => {
    if (paymentMode === "solo-full") {
      return {
        monthlyAmount: 0,
        totalAmount: totalPrice,
        perPersonTotal: totalPrice,
        perPersonMonthly: 0,
      };
    }

    const interestRate = getInterestRate(termMonths);
    const totalWithInterest = totalPrice * (1 + interestRate / 100);
    const monthlyPayment = totalWithInterest / termMonths;
    const perPersonTotal = totalWithInterest / contributorCount;
    const perPersonMonthly = monthlyPayment / contributorCount;

    return {
      monthlyAmount: monthlyPayment,
      totalAmount: totalWithInterest,
      perPersonTotal,
      perPersonMonthly,
    };
  };

  const payments = calculatePayments();

  // Update payment mode based on contributor count
  useEffect(() => {
    if (contributorCount === 1) {
      setPaymentMode("solo-layby"); // Default to layby for solo
    } else {
      setPaymentMode("group");
    }
  }, [contributorCount]);

  // Sync state to localStorage for sticky footer
  useEffect(() => {
    const state = {
      contributorCount,
      paymentMode,
      termMonths,
      invitedMembers: invitedMembers.map((m) => m.contact),
    };
    localStorage.setItem("sliderPaymentFlowState", JSON.stringify(state));

    // Dispatch custom event to notify sticky footer
    window.dispatchEvent(new CustomEvent("sliderPaymentFlowChange"));
  }, [contributorCount, paymentMode, termMonths, invitedMembers]);

  // Get dynamic microcopy based on slider position
  const getMicrocopy = () => {
    if (contributorCount === 1)
      return "You're buying solo - choose your payment plan below";
    if (contributorCount <= 3)
      return "Small group purchase - perfect for couples or close friends";
    if (contributorCount <= 6)
      return "Group purchase - great for families or small teams";
    return "Large group - ideal for communities or organizations";
  };

  const addMember = () => {
    if (newMemberContact.trim()) {
      setInvitedMembers([
        ...invitedMembers,
        {
          contact: newMemberContact.trim(),
          type: contactType,
        },
      ]);
      setNewMemberContact("");
    }
  };

  const removeMember = (index: number) => {
    setInvitedMembers(invitedMembers.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      {/* Step 1: Contributor Slider */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            How many people are contributing?
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Slider */}
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>Solo</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>Group</span>
              </div>
            </div>

            <div className="relative">
              <input
                type="range"
                min="1"
                max="10"
                value={contributorCount}
                onChange={(e) => setContributorCount(parseInt(e.target.value))}
                className={`w-full ${styles.slider} ${
                  contributorCount === 1 ? styles.solo : styles.group
                }`}
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                {Array.from({ length: 10 }, (_, i) => (
                  <span key={i + 1}>{i + 1}</span>
                ))}
              </div>
            </div>

            <div className="text-center">
              <div className="text-lg font-semibold">
                {contributorCount}{" "}
                {contributorCount === 1 ? "person" : "people"}
              </div>
              <div className="text-sm text-muted-foreground">
                {getMicrocopy()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 2: Payment Options */}
      {contributorCount === 1 ? (
        /* Solo Purchase Options */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Solo Purchase
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {/* Pay Full Option */}
              <div
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  paymentMode === "solo-full"
                    ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                    : "border-border hover:bg-accent/50"
                }`}
                onClick={() => setPaymentMode("solo-full")}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div
                      className={`p-2 rounded-lg ${
                        paymentMode === "solo-full"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted"
                      }`}
                    >
                      <CreditCard className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">Pay Full Amount Now</div>
                      <div className="text-sm text-muted-foreground">
                        {formatPrice(totalPrice)} • Get it faster
                      </div>
                    </div>
                  </div>
                  <div
                    className={`w-4 h-4 rounded-full border-2 ${
                      paymentMode === "solo-full"
                        ? "border-primary bg-primary"
                        : "border-muted-foreground"
                    }`}
                  >
                    {paymentMode === "solo-full" && (
                      <div className="w-2 h-2 bg-primary-foreground rounded-full m-0.5" />
                    )}
                  </div>
                </div>
              </div>

              {/* Pay Monthly Option */}
              <div
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  paymentMode === "solo-layby"
                    ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                    : "border-border hover:bg-accent/50"
                }`}
                onClick={() => setPaymentMode("solo-layby")}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div
                      className={`p-2 rounded-lg ${
                        paymentMode === "solo-layby"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted"
                      }`}
                    >
                      <Calendar className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium flex items-center gap-2">
                        Pay Monthly (Layby)
                        <Badge variant="secondary">Popular</Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Spread the cost with flexible monthly payments
                      </div>
                    </div>
                  </div>
                  <div
                    className={`w-4 h-4 rounded-full border-2 ${
                      paymentMode === "solo-layby"
                        ? "border-primary bg-primary"
                        : "border-muted-foreground"
                    }`}
                  >
                    {paymentMode === "solo-layby" && (
                      <div className="w-2 h-2 bg-primary-foreground rounded-full m-0.5" />
                    )}
                  </div>
                </div>

                {/* Term Slider for Solo Layby */}
                {paymentMode === "solo-layby" && (
                  <div className="space-y-3 pt-3 border-t">
                    <Label>Choose your term: {termMonths} months</Label>
                    <div className="space-y-2">
                      <input
                        type="range"
                        min="3"
                        max="36"
                        step="3"
                        value={termMonths}
                        onChange={(e) =>
                          setTermMonths(parseInt(e.target.value) as LaybyTerm)
                        }
                        className={`w-full ${styles.termSlider}`}
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>3m</span>
                        <span>36m</span>
                      </div>
                    </div>
                    <div className="bg-muted p-3 rounded-lg">
                      <div className="text-lg font-semibold text-primary">
                        {formatPrice(payments.monthlyAmount)}/month
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Total: {formatPrice(payments.totalAmount)} (includes
                        interest)
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Group Purchase Options */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Group Purchase ({contributorCount} people)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Term Slider for Group */}
            <div className="space-y-3">
              <Label>Payment term for your group: {termMonths} months</Label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="6"
                  max="36"
                  step="3"
                  value={termMonths}
                  onChange={(e) =>
                    setTermMonths(parseInt(e.target.value) as LaybyTerm)
                  }
                  className={`w-full ${styles.termSlider} ${styles.group}`}
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>6m</span>
                  <span>36m</span>
                </div>
              </div>
            </div>

            {/* Payment Summary */}
            <div className="bg-muted p-4 rounded-lg">
              <div className="text-lg font-semibold text-primary mb-1">
                Each person pays: {formatPrice(payments.perPersonMonthly)}/month
              </div>
              <div className="text-sm text-muted-foreground">
                Total per person: {formatPrice(payments.perPersonTotal)}{" "}
                (includes interest)
              </div>
            </div>

            {/* Group Summary */}
            <Card className="bg-accent/50">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Group Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Product Price:</span>
                  <span>{formatPrice(totalPrice)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Group Size:</span>
                  <span>{contributorCount} people</span>
                </div>
                <div className="flex justify-between">
                  <span>Per Person:</span>
                  <span>
                    {formatPrice(payments.perPersonTotal)} (
                    {formatPrice(payments.perPersonMonthly)}/month)
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold">
                  <span>Group Total:</span>
                  <span>{formatPrice(payments.totalAmount)}</span>
                </div>
                <div className="text-xs text-muted-foreground mt-2">
                  🎯 Goal: Everyone commits by{" "}
                  {new Date(
                    Date.now() + 14 * 24 * 60 * 60 * 1000
                  ).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>

            {/* Group Invite Section */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <UserPlus className="h-4 w-4" />
                  Invite your group members (optional now)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Add Member Input */}
                <div className="flex gap-2">
                  <div className="flex-1 flex gap-2">
                    <select
                      value={contactType}
                      onChange={(e) =>
                        setContactType(e.target.value as "email" | "phone")
                      }
                      className="px-3 py-2 border rounded-md bg-background"
                    >
                      <option value="email">📧</option>
                      <option value="phone">📱</option>
                    </select>
                    <Input
                      placeholder={
                        contactType === "email"
                          ? "<EMAIL>"
                          : "+27 82 123 4567"
                      }
                      value={newMemberContact}
                      onChange={(e) => setNewMemberContact(e.target.value)}
                      onKeyDown={(e) => e.key === "Enter" && addMember()}
                    />
                  </div>
                  <Button
                    onClick={addMember}
                    size="sm"
                    disabled={!newMemberContact.trim()}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                {/* Invited Members List */}
                {invitedMembers.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Invited Members:
                    </Label>
                    {invitedMembers.map((member, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-accent/50 rounded-md"
                      >
                        <div className="flex items-center gap-2">
                          {member.type === "email" ? (
                            <Mail className="h-4 w-4" />
                          ) : (
                            <Phone className="h-4 w-4" />
                          )}
                          <span className="text-sm">{member.contact}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeMember(index)}
                          className="h-6 w-6 p-0"
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                <div className="text-xs text-muted-foreground">
                  💡 You can invite others after creating the group
                </div>
              </CardContent>
            </Card>
          </CardContent>
        </Card>
      )}

      {/* Summary Section - No longer includes buttons */}
      <Card>
        <CardContent className="pt-6">
          {contributorCount === 1 ? (
            /* Solo Purchase Summary */
            <div className="text-center space-y-2">
              <div className="text-lg font-semibold">Ready for Checkout</div>
              <div className="text-muted-foreground">
                Solo {paymentMode === "solo-full" ? "Cash" : "Layby"} Purchase
              </div>
              {paymentMode === "solo-layby" && (
                <div className="text-primary font-medium">
                  {formatPrice(payments.monthlyAmount)}/month for {termMonths}{" "}
                  months
                </div>
              )}
              {paymentMode === "solo-full" && (
                <div className="text-primary font-medium">
                  {formatPrice(payments.totalAmount)} total
                </div>
              )}
            </div>
          ) : (
            /* Group Purchase Summary */
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-lg font-semibold mb-2">
                  Group Ready to Create
                </div>
                <div className="text-muted-foreground">
                  {contributorCount}-Person Group Purchase
                </div>
                <div className="text-primary font-medium">
                  {formatPrice(payments.perPersonMonthly)}/month per person for{" "}
                  {termMonths} months
                </div>
              </div>

              <div className="bg-muted p-3 rounded-lg text-center">
                <div className="text-sm font-medium mb-1">Next Steps:</div>
                <div className="text-xs text-muted-foreground">
                  Create group → Get shareable link → Invite members
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add bottom padding to prevent content from being hidden behind sticky footer */}
      <div className="h-20"></div>
    </div>
  );
}
