"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { SliderPaymentFlow } from "./slider-payment-flow";
import { Product } from "@/data/products";
import { formatPrice } from "@/lib/utils";
import { X, ArrowLeft, ShoppingCart, UserPlus, CreditCard } from "lucide-react";

interface SliderPurchaseDrawerProps {
  product: Product;
  quantity: number;
  children: React.ReactNode; // The trigger button
}

interface PaymentFlowData {
  contributorCount: number;
  paymentMode: "solo-full" | "solo-layby" | "group";
  termMonths?: number;
  monthlyAmount: number;
  totalAmount: number;
  invitedMembers: string[];
}

export function SliderPurchaseDrawer({
  product,
  quantity,
  children,
}: SliderPurchaseDrawerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [paymentData, setPaymentData] = useState<PaymentFlowData | null>(null);

  const handleClose = () => {
    setIsOpen(false);
    setShowConfirmation(false);
    setPaymentData(null);
  };

  const handleContinue = (data: PaymentFlowData) => {
    setPaymentData(data);
    setShowConfirmation(true);
  };

  const handleBackToFlow = () => {
    setShowConfirmation(false);
  };

  const handleFinalContinue = () => {
    // Here you would typically navigate to checkout or group creation
    console.log("Final payment data:", paymentData);

    if (paymentData?.paymentMode === "group") {
      // Navigate to group creation/management page
      window.location.href = `/groups/create?product=${
        product.id
      }&data=${encodeURIComponent(JSON.stringify(paymentData))}`;
    } else {
      // Navigate to checkout
      window.location.href = `/checkout?product=${
        product.id
      }&data=${encodeURIComponent(JSON.stringify(paymentData))}`;
    }

    handleClose();
  };

  const getDrawerTitle = () => {
    if (showConfirmation && paymentData) {
      if (paymentData.paymentMode === "group") {
        return `Group Purchase (${paymentData.contributorCount} people)`;
      } else {
        return paymentData.paymentMode === "solo-full"
          ? "Cash Purchase"
          : "Layby Purchase";
      }
    }
    return "Select Payment Options";
  };

  const getDrawerDescription = () => {
    if (showConfirmation && paymentData) {
      const totalPrice = formatPrice(product.price * quantity);
      if (paymentData.paymentMode === "group") {
        return `Share ${totalPrice} with ${paymentData.contributorCount} people`;
      } else if (paymentData.paymentMode === "solo-full") {
        return `Pay ${totalPrice} upfront`;
      } else {
        return `Pay ${formatPrice(paymentData.monthlyAmount)}/month for ${
          paymentData.termMonths
        } months`;
      }
    }
    return "Choose how you'd like to purchase this product";
  };

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild onClick={() => setIsOpen(true)}>
        {children}
      </DrawerTrigger>
      <DrawerContent className="max-h-[90vh]">
        <DrawerHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {showConfirmation && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleBackToFlow}
                  className="h-8 w-8"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
              <div className="flex-1">
                <DrawerTitle>{getDrawerTitle()}</DrawerTitle>
                <DrawerDescription>{getDrawerDescription()}</DrawerDescription>
              </div>
            </div>
            <DrawerClose asChild>
              <Button variant="ghost" size="icon" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-y-auto px-4 pb-4">
          {!showConfirmation ? (
            <SliderPaymentFlow product={product} quantity={quantity} />
          ) : (
            <ConfirmationView
              product={product}
              quantity={quantity}
              paymentData={paymentData!}
              onContinue={handleFinalContinue}
            />
          )}
        </div>

        {/* Sticky Footer with CTA */}
        {!showConfirmation && (
          <StickyFooter
            product={product}
            quantity={quantity}
            onContinue={handleContinue}
          />
        )}
      </DrawerContent>
    </Drawer>
  );
}

interface ConfirmationViewProps {
  product: Product;
  quantity: number;
  paymentData: PaymentFlowData;
  onContinue: () => void;
}

function ConfirmationView({
  product,
  quantity,
  paymentData,
  onContinue,
}: ConfirmationViewProps) {
  const totalPrice = product.price * quantity;

  return (
    <div className="space-y-6">
      {/* Product Summary */}
      <div className="bg-accent/50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Order Summary</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>{product.name}</span>
            <span>{formatPrice(product.price)}</span>
          </div>
          <div className="flex justify-between">
            <span>Quantity:</span>
            <span>{quantity}</span>
          </div>
          <div className="flex justify-between font-semibold border-t pt-2">
            <span>Subtotal:</span>
            <span>{formatPrice(totalPrice)}</span>
          </div>
        </div>
      </div>

      {/* Payment Summary */}
      <div className="bg-primary/5 p-4 rounded-lg border border-primary/20">
        <h3 className="font-semibold mb-3 text-primary">Payment Plan</h3>

        {paymentData.paymentMode === "solo-full" && (
          <div className="space-y-2">
            <div className="text-lg font-semibold">
              Pay Full Amount: {formatPrice(paymentData.totalAmount)}
            </div>
            <div className="text-sm text-muted-foreground">
              One-time payment • No interest charges
            </div>
          </div>
        )}

        {paymentData.paymentMode === "solo-layby" && (
          <div className="space-y-2">
            <div className="text-lg font-semibold">
              Monthly Payments: {formatPrice(paymentData.monthlyAmount)}
              /month
            </div>
            <div className="text-sm text-muted-foreground">
              {paymentData.termMonths} months • Total:{" "}
              {formatPrice(paymentData.totalAmount)}
            </div>
          </div>
        )}

        {paymentData.paymentMode === "group" && (
          <div className="space-y-3">
            <div className="text-lg font-semibold">
              Group Purchase: {paymentData.contributorCount} people
            </div>
            <div className="bg-background p-3 rounded-md">
              <div className="text-sm font-medium mb-1">Your share:</div>
              <div className="text-lg font-semibold text-primary">
                {formatPrice(paymentData.monthlyAmount)}/month for{" "}
                {paymentData.termMonths} months
              </div>
              <div className="text-sm text-muted-foreground">
                Total per person: {formatPrice(paymentData.totalAmount)}
              </div>
            </div>
            {paymentData.invitedMembers.length > 0 && (
              <div>
                <div className="text-sm font-medium mb-2">Invited members:</div>
                <div className="space-y-1">
                  {paymentData.invitedMembers.map((member, index) => (
                    <div key={index} className="text-sm text-muted-foreground">
                      • {member}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Next Steps */}
      <div className="bg-muted p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Next Steps</h3>
        <div className="text-sm text-muted-foreground space-y-1">
          {paymentData.paymentMode === "group" ? (
            <>
              <div>• Create your group and get a shareable link</div>
              <div>• Invite members to join your group</div>
              <div>• Start payments once everyone commits</div>
            </>
          ) : (
            <>
              <div>• Review your payment details</div>
              <div>• Complete secure checkout</div>
              <div>• Receive order confirmation</div>
            </>
          )}
        </div>
      </div>

      {/* Continue Button */}
      <Button onClick={onContinue} className="w-full" size="lg">
        {paymentData.paymentMode === "group"
          ? "Create Group & Continue"
          : "Continue to Checkout"}
      </Button>
    </div>
  );
}

interface StickyFooterProps {
  product: Product;
  quantity: number;
  onContinue: (data: PaymentFlowData) => void;
}

function StickyFooter({ product, quantity, onContinue }: StickyFooterProps) {
  const [contributorCount, setContributorCount] = useState(1);
  const [paymentMode, setPaymentMode] = useState<
    "solo-full" | "solo-layby" | "group"
  >("solo-layby");
  const [termMonths, setTermMonths] = useState(12);
  const [invitedMembers, setInvitedMembers] = useState<string[]>([]);

  // Listen for changes from the main flow component
  useEffect(() => {
    const handleStorageChange = () => {
      const flowState = localStorage.getItem("sliderPaymentFlowState");
      if (flowState) {
        const state = JSON.parse(flowState);
        setContributorCount(state.contributorCount || 1);
        setPaymentMode(state.paymentMode || "solo-layby");
        setTermMonths(state.termMonths || 12);
        setInvitedMembers(state.invitedMembers || []);
      }
    };

    // Listen for custom events from the main component
    window.addEventListener("sliderPaymentFlowChange", handleStorageChange);
    handleStorageChange(); // Initial load

    return () => {
      window.removeEventListener(
        "sliderPaymentFlowChange",
        handleStorageChange
      );
    };
  }, []);

  const totalPrice = product.price * quantity;

  // Calculate payments (same logic as in main component)
  const getInterestRate = (months: number) => {
    const rates: Record<number, number> = {
      3: 5,
      6: 8,
      9: 10,
      12: 12,
      18: 15,
      24: 18,
      36: 22,
    };
    return rates[months] || 12;
  };

  const calculatePayments = () => {
    if (paymentMode === "solo-full") {
      return {
        monthlyAmount: 0,
        totalAmount: totalPrice,
        perPersonTotal: totalPrice,
        perPersonMonthly: 0,
      };
    }

    const interestRate = getInterestRate(termMonths);
    const totalWithInterest = totalPrice * (1 + interestRate / 100);
    const monthlyPayment = totalWithInterest / termMonths;
    const perPersonTotal = totalWithInterest / contributorCount;
    const perPersonMonthly = monthlyPayment / contributorCount;

    return {
      monthlyAmount: monthlyPayment,
      totalAmount: totalWithInterest,
      perPersonTotal,
      perPersonMonthly,
    };
  };

  const payments = calculatePayments();

  const handleContinue = () => {
    const data: PaymentFlowData = {
      contributorCount,
      paymentMode,
      termMonths: paymentMode !== "solo-full" ? termMonths : undefined,
      monthlyAmount:
        contributorCount > 1
          ? payments.perPersonMonthly
          : payments.monthlyAmount,
      totalAmount:
        contributorCount > 1 ? payments.perPersonTotal : payments.totalAmount,
      invitedMembers: invitedMembers,
    };
    onContinue(data);
  };

  return (
    <div className="sticky bottom-0 left-0 right-0 bg-white border-t p-4 z-50">
      <div className="space-y-3">
        {/* Quick Summary */}
        <div className="text-center">
          {contributorCount === 1 ? (
            <div className="text-sm text-muted-foreground">
              {paymentMode === "solo-full"
                ? `Pay ${formatPrice(payments.totalAmount)} total`
                : `${formatPrice(
                    payments.monthlyAmount
                  )}/month for ${termMonths} months`}
            </div>
          ) : (
            <div className="text-sm text-muted-foreground">
              {formatPrice(payments.perPersonMonthly)}/month per person (
              {contributorCount} people)
            </div>
          )}
        </div>

        {/* CTA Buttons */}
        {contributorCount === 1 ? (
          <Button onClick={handleContinue} className="w-full" size="lg">
            <ShoppingCart className="mr-2 h-4 w-4" />
            Continue to Secure Checkout
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              onClick={handleContinue}
              variant="outline"
              className="flex-1"
              size="lg"
            >
              <UserPlus className="mr-2 h-4 w-4" />
              Invite Members
            </Button>
            <Button onClick={handleContinue} className="flex-1" size="lg">
              <CreditCard className="mr-2 h-4 w-4" />
              Pay My Share
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
