"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { X, SlidersHorizontal } from "lucide-react";

// Mock filter options until we have real data
export const filterOptions = [
  {
    id: "category",
    name: "Category",
    options: [
      { id: "category-1", name: "Electronics", value: "electronics" },
      { id: "category-2", name: "Furniture", value: "furniture" },
      { id: "category-3", name: "Clothing", value: "clothing" },
      { id: "category-4", name: "Accessories", value: "accessories" },
      { id: "category-5", name: "Home Decor", value: "home-decor" },
    ],
  },
  {
    id: "priceRange",
    name: "Price Range",
    options: [
      { id: "price-1", name: "Under $100", value: "under-100" },
      { id: "price-2", name: "$100 - $250", value: "100-250" },
      { id: "price-3", name: "$250 - $500", value: "250-500" },
      { id: "price-4", name: "Over $500", value: "over-500" },
    ],
  },
  {
    id: "manufacturingTime",
    name: "Manufacturing Time",
    options: [
      { id: "time-1", name: "Under 7 days", value: "under-7" },
      { id: "time-2", name: "7-14 days", value: "7-14" },
      { id: "time-3", name: "14-28 days", value: "14-28" },
      { id: "time-4", name: "Over 28 days", value: "over-28" },
    ],
  },
  {
    id: "rating",
    name: "Rating",
    options: [
      { id: "rating-1", name: "4★ & Above", value: "4" },
      { id: "rating-2", name: "3★ & Above", value: "3" },
      { id: "rating-3", name: "2★ & Above", value: "2" },
    ],
  },
];

interface FilterOption {
  id: string;
  name: string;
  value: string;
}

interface FilterCategory {
  id: string;
  name: string;
  options: FilterOption[];
}

interface FilterSheetProps {
  activeFilters: Record<string, string[]>;
  setActiveFilters: (filters: Record<string, string[]>) => void;
}

export function FilterSheet({
  activeFilters,
  setActiveFilters,
}: FilterSheetProps) {
  const [tempFilters, setTempFilters] =
    useState<Record<string, string[]>>(activeFilters);
  const [isOpen, setIsOpen] = useState(false);

  const toggleFilter = (categoryId: string, optionValue: string) => {
    setTempFilters((prev) => {
      const categoryFilters = prev[categoryId] || [];
      const newCategoryFilters = categoryFilters.includes(optionValue)
        ? categoryFilters.filter((v) => v !== optionValue)
        : [...categoryFilters, optionValue];

      return {
        ...prev,
        [categoryId]: newCategoryFilters,
      };
    });
  };

  const applyFilters = () => {
    setActiveFilters(tempFilters);
    setIsOpen(false);
  };

  const resetFilters = () => {
    const emptyFilters: Record<string, string[]> = {};
    filterOptions.forEach((category) => {
      emptyFilters[category.id] = [];
    });
    setTempFilters(emptyFilters);
    setActiveFilters(emptyFilters);
    setIsOpen(false);
  };

  const getTotalActiveFilters = () => {
    return Object.values(activeFilters).reduce(
      (total, filterValues) => total + filterValues.length,
      0
    );
  };

  const activeFilterCount = getTotalActiveFilters();

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <SlidersHorizontal className="h-4 w-4" />
          <span>Filters</span>
          {activeFilterCount > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent
        side="bottom"
        className="h-[85vh] px-0 sm:max-w-md sm:rounded-t-xl"
      >
        <SheetHeader className="px-4">
          <SheetTitle className="text-lg font-semibold">
            Filter Products
          </SheetTitle>
        </SheetHeader>

        <div className="overflow-y-auto py-4 px-4 h-[calc(85vh-160px)]">
          {filterOptions.map((category: FilterCategory) => (
            <div key={category.id} className="mb-6">
              <h3 className="text-sm font-medium mb-3">{category.name}</h3>
              <div className="flex flex-wrap gap-2">
                {category.options.map((option) => {
                  const isActive = (tempFilters[category.id] || []).includes(
                    option.value
                  );
                  return (
                    <Badge
                      key={option.id}
                      variant={isActive ? "default" : "outline"}
                      className={`cursor-pointer rounded-md py-1.5 px-3 ${
                        isActive ? "bg-primary text-primary-foreground" : ""
                      }`}
                      onClick={() => toggleFilter(category.id, option.value)}
                    >
                      {option.name}
                    </Badge>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        <SheetFooter className="px-4 pt-2 border-t sticky bottom-0 bg-white">
          <div className="w-full flex items-center justify-between gap-2">
            <Button variant="outline" className="flex-1" onClick={resetFilters}>
              Clear All
            </Button>
            <Button className="flex-1" onClick={applyFilters}>
              Apply Filters
            </Button>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
