"use client";

import { useState, useEffect, useMemo } from "react";
import {
  Product,
  products as allProducts,
  sortOptions,
  calculateMinimumMonthlyInstallment,
} from "@/data/products";
import { ProductCard } from "@/components/product/product-card";
import { FilterSheet } from "@/components/product/filter-sheet";
import { SortSelector } from "@/components/product/sort-selector";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  LayoutGrid,
  LayoutList,
  Search,
  SlidersHorizontal,
  Smartphone,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { getAllStoneImages } from "@/lib/stone-images";

type ViewMode = "grid" | "list" | "feed";

export function ProductGrid() {
  // Create individual stone products from all 188 available stone images
  const individualStoneProducts = useMemo(() => {
    const stoneProducts: Product[] = [];
    const allStoneImages = getAllStoneImages();

    // Define category ranges for stone numbers (based on stone-images.ts)
    const getCategoryForStoneNumber = (stoneNumber: number): string => {
      if (stoneNumber >= 1 && stoneNumber <= 40) return "traditional";
      if (stoneNumber >= 41 && stoneNumber <= 80) return "modern";
      if (stoneNumber >= 81 && stoneNumber <= 120) return "custom";
      if (stoneNumber >= 121 && stoneNumber <= 160) return "premium";
      if (stoneNumber >= 161 && stoneNumber <= 188) return "compact";
      return "traditional"; // fallback
    };

    // Get a base product template for each category
    const getBaseProductForCategory = (category: string): Product => {
      const baseProduct =
        allProducts.find((p) => p.category === category) || allProducts[0];
      return baseProduct;
    };

    // Create a product for each stone image
    allStoneImages.forEach((imagePath) => {
      // Extract stone number from path
      const match = imagePath.match(/stone-(\d+)\.jpeg/);
      const stoneNumber = match ? parseInt(match[1]) : 0;

      if (stoneNumber > 0) {
        const category = getCategoryForStoneNumber(stoneNumber);
        const baseProduct = getBaseProductForCategory(category);

        // Create a new product for each stone image
        const stoneProduct: Product = {
          ...baseProduct,
          id: `stone-${stoneNumber}`,
          name: `Stone ${stoneNumber} - ${
            category.charAt(0).toUpperCase() + category.slice(1)
          }`,
          images: [imagePath], // Only this specific stone image
          description: `Individual ${category} memorial stone. ${baseProduct.description}`,
          category: category,
        };

        stoneProducts.push(stoneProduct);
      }
    });

    // Sort by stone number
    return stoneProducts.sort((a, b) => {
      const aMatch = a.images[0].match(/stone-(\d+)\.jpeg/);
      const bMatch = b.images[0].match(/stone-(\d+)\.jpeg/);
      const aNum = aMatch ? parseInt(aMatch[1]) : 0;
      const bNum = bMatch ? parseInt(bMatch[1]) : 0;
      return aNum - bNum;
    });
  }, []);

  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>({
    category: [],
    priceRange: [],
    manufacturingTime: [],
    rating: [],
  });
  const [sortBy, setSortBy] = useState("newest");
  const [viewMode, setViewMode] = useState<ViewMode>("feed");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading state for demo purposes
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
      setFilteredProducts(individualStoneProducts);
    }, 1000);

    return () => clearTimeout(timer);
  }, [individualStoneProducts]);

  // Apply filters, sorting, and search
  useEffect(() => {
    let result = [...individualStoneProducts];

    // Apply search
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (product) =>
          product.name.toLowerCase().includes(query) ||
          product.description.toLowerCase().includes(query) ||
          product.category.toLowerCase().includes(query) ||
          product.merchant.name.toLowerCase().includes(query)
      );
    }

    // Apply filters
    Object.entries(activeFilters).forEach(([categoryId, values]) => {
      if (values.length === 0) return;

      switch (categoryId) {
        case "category":
          result = result.filter((product) =>
            values.some((value) => product.category.toLowerCase() === value)
          );
          break;
        case "priceRange":
          result = result.filter((product) => {
            return values.some((value) => {
              if (value === "under-100") return product.price < 100;
              if (value === "100-250")
                return product.price >= 100 && product.price <= 250;
              if (value === "250-500")
                return product.price >= 250 && product.price <= 500;
              if (value === "over-500") return product.price > 500;
              return true;
            });
          });
          break;
        case "manufacturingTime":
          result = result.filter((product) => {
            const days = product.manufacturingTime || 0;
            return values.some((value) => {
              if (value === "under-7") return days < 7;
              if (value === "7-14") return days >= 7 && days <= 14;
              if (value === "14-28") return days > 14 && days <= 28;
              if (value === "over-28") return days > 28;
              return true;
            });
          });
          break;
        case "rating":
          result = result.filter((product) => {
            return values.some((value) => {
              const minRating = Number(value);
              return product.rating >= minRating;
            });
          });
          break;
      }
    });

    // Apply sorting
    switch (sortBy) {
      case "price-asc":
        result.sort((a, b) => a.price - b.price);
        break;
      case "price-desc":
        result.sort((a, b) => b.price - a.price);
        break;
      case "rating":
        result.sort((a, b) => b.rating - a.rating);
        break;
      case "monthly-asc":
        result.sort(
          (a, b) =>
            calculateMinimumMonthlyInstallment(a.price) -
            calculateMinimumMonthlyInstallment(b.price)
        );
        break;
      case "newest":
      default:
        // Default is "newest" which is the default order of our mock data
        break;
    }

    setFilteredProducts(result);
  }, [activeFilters, sortBy, searchQuery, individualStoneProducts]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const resetFilters = () => {
    setActiveFilters({
      category: [],
      priceRange: [],
      manufacturingTime: [],
      rating: [],
    });
  };

  // Skeletons for loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-12 w-full" />
        <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
          {Array(8)
            .fill(0)
            .map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="aspect-square h-auto w-full rounded-lg" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-6 w-1/2" />
              </div>
            ))}
        </div>
      </div>
    );
  }

  // No products found state
  if (filteredProducts.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Input
            type="search"
            placeholder="Search products..."
            value={searchQuery}
            onChange={handleSearch}
            className="flex-1"
          />

          <Button
            variant="outline"
            size="icon"
            onClick={() => setIsFilterOpen(true)}
            aria-label="Filter"
          >
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            {filteredProducts.length} product
            {filteredProducts.length > 1 ? "s" : ""}
          </div>

          <div className="flex items-center gap-2">
            <SortSelector
              options={sortOptions}
              value={sortBy}
              onValueChange={setSortBy}
            />

            <div className="flex rounded-md border">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="icon"
                className="h-9 w-9 rounded-none rounded-l-md"
                onClick={() => setViewMode("grid")}
                aria-label="Grid view"
              >
                <LayoutGrid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="icon"
                className="h-9 w-9 rounded-none"
                onClick={() => setViewMode("list")}
                aria-label="List view"
              >
                <LayoutList className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "feed" ? "default" : "ghost"}
                size="icon"
                className="h-9 w-9 rounded-none rounded-r-md"
                onClick={() => setViewMode("feed")}
                aria-label="Feed view"
              >
                <Smartphone className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <FilterSheet
          activeFilters={activeFilters}
          setActiveFilters={setActiveFilters}
        />

        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
          <Search className="mb-2 h-8 w-8 text-muted-foreground" />
          <h3 className="text-lg font-medium">No products found</h3>
          <p className="text-sm text-muted-foreground">
            Try adjusting your search or filters
          </p>
          <Button variant="outline" className="mt-4" onClick={resetFilters}>
            Reset filters
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Input
          type="search"
          placeholder="Search products..."
          value={searchQuery}
          onChange={handleSearch}
          className="flex-1"
        />

        <Button
          variant="outline"
          size="icon"
          onClick={() => setIsFilterOpen(true)}
          aria-label="Filter"
        >
          <SlidersHorizontal className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {filteredProducts.length} product
          {filteredProducts.length > 1 ? "s" : ""}
        </div>

        <div className="flex items-center gap-2">
          <SortSelector
            options={sortOptions}
            value={sortBy}
            onValueChange={setSortBy}
          />

          <div className="flex rounded-md border">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="icon"
              className="h-9 w-9 rounded-none rounded-l-md"
              onClick={() => setViewMode("grid")}
              aria-label="Grid view"
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="icon"
              className="h-9 w-9 rounded-none"
              onClick={() => setViewMode("list")}
              aria-label="List view"
            >
              <LayoutList className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "feed" ? "default" : "ghost"}
              size="icon"
              className="h-9 w-9 rounded-none rounded-r-md"
              onClick={() => setViewMode("feed")}
              aria-label="Feed view"
            >
              <Smartphone className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <FilterSheet
        activeFilters={activeFilters}
        setActiveFilters={setActiveFilters}
      />

      <div
        className={
          viewMode === "grid"
            ? "grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4"
            : viewMode === "feed"
            ? "max-w-md mx-auto space-y-6"
            : "space-y-4"
        }
      >
        {filteredProducts.map((product) => (
          <ProductCard key={product.id} product={product} viewMode={viewMode} />
        ))}
      </div>
    </div>
  );
}
