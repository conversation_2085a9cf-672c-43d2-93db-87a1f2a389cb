@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #fafafa;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    /* Ensure body can always scroll */
    overflow-y: auto !important;
  }

  /* Fix for drawer scroll lock issues */
  html {
    overflow-y: auto !important;
  }
}

/* Mobile-first utilities */
@layer utilities {
  .touch-target {
    @apply min-h-[44px] min-w-[44px]; /* Minimum touch target size */
  }

  .mobile-container {
    @apply px-4 w-full max-w-md mx-auto;
  }

  .bottom-safe {
    padding-bottom: env(safe-area-inset-bottom, 1rem);
  }

  .top-safe {
    padding-top: env(safe-area-inset-top, 1rem);
  }

  /* Fluid typography */
  .text-fluid-sm {
    font-size: clamp(0.875rem, 0.8vw + 0.75rem, 1rem);
  }

  .text-fluid-base {
    font-size: clamp(1rem, 1vw + 0.85rem, 1.125rem);
  }

  .text-fluid-lg {
    font-size: clamp(1.125rem, 1.5vw + 0.9rem, 1.25rem);
  }

  .text-fluid-xl {
    font-size: clamp(1.25rem, 2vw + 1rem, 1.5rem);
  }

  .text-fluid-2xl {
    font-size: clamp(1.5rem, 2.5vw + 1.1rem, 1.875rem);
  }

  .text-fluid-3xl {
    font-size: clamp(1.875rem, 3vw + 1.2rem, 2.25rem);
  }
}

@layer components {
  .bg-card {
    @apply drop-shadow-xl;
    @apply border-none;
    @apply rounded-full;
  }
}
