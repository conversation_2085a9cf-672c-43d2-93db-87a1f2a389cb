"use client";

import { useState, useMemo } from "react";
import { notFound } from "next/navigation";
import {
  products,
  Product,
  calculateMinimumMonthlyInstallment,
} from "@/data/products";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { getAllStoneImages } from "@/lib/stone-images";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  StarIcon,
  Clock,
  Truck,
  Plus,
  Minus,
  Share,
  ShoppingCart,
} from "lucide-react";
import Image from "next/image";
import { formatPrice } from "@/lib/utils";
import { WishlistButton } from "@/components/wishlist/wishlist-button";
import { PurchaseJourneyDrawer } from "@/components/payment/purchase-journey-drawer";

interface ProductDetailPageProps {
  params: {
    id: string;
  };
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  // Create the same individual stone products as in ProductGrid
  const allProducts = useMemo(() => {
    const stoneProducts: Product[] = [];
    const allStoneImages = getAllStoneImages();

    // Define category ranges for stone numbers (based on stone-images.ts)
    const getCategoryForStoneNumber = (stoneNumber: number): string => {
      if (stoneNumber >= 1 && stoneNumber <= 40) return "traditional";
      if (stoneNumber >= 41 && stoneNumber <= 80) return "modern";
      if (stoneNumber >= 81 && stoneNumber <= 120) return "custom";
      if (stoneNumber >= 121 && stoneNumber <= 160) return "premium";
      if (stoneNumber >= 161 && stoneNumber <= 188) return "compact";
      return "traditional"; // fallback
    };

    // Get a base product template for each category
    const getBaseProductForCategory = (category: string): Product => {
      const baseProduct =
        products.find((p) => p.category === category) || products[0];
      return baseProduct;
    };

    // Create a product for each stone image
    allStoneImages.forEach((imagePath) => {
      // Extract stone number from path
      const match = imagePath.match(/stone-(\d+)\.jpeg/);
      const stoneNumber = match ? parseInt(match[1]) : 0;

      if (stoneNumber > 0) {
        const category = getCategoryForStoneNumber(stoneNumber);
        const baseProduct = getBaseProductForCategory(category);

        // Create a new product for each stone image
        const stoneProduct: Product = {
          ...baseProduct,
          id: `stone-${stoneNumber}`,
          name: `Stone ${stoneNumber} - ${
            category.charAt(0).toUpperCase() + category.slice(1)
          }`,
          images: [imagePath], // Only this specific stone image
          description: `Individual ${category} memorial stone. ${baseProduct.description}`,
          category: category,
        };

        stoneProducts.push(stoneProduct);
      }
    });

    // Include both original products and stone products
    return [...products, ...stoneProducts];
  }, []);

  const product = allProducts.find((p) => p.id === params.id);

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);

  if (!product) {
    return notFound();
  }

  const incrementQuantity = () => {
    setQuantity((prev) => prev + 1);
  };

  const decrementQuantity = () => {
    setQuantity((prev) => (prev > 1 ? prev - 1 : 1));
  };

  return (
    <MobileLayout showBackButton>
      <div className="pb-24">
        {/* Product Images */}
        <div className="relative aspect-square overflow-hidden mb-6">
          <Image
            src={product.images[currentImageIndex] || "/images/placeholder.png"}
            alt={product.name}
            fill
            priority
            className="object-cover"
          />

          {/* Image carousel navigation dots */}
          {product.images.length > 1 && (
            <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-1.5">
              {product.images.map((_, index) => (
                <button
                  key={index}
                  className={`h-2 w-2 rounded-full ${
                    index === currentImageIndex
                      ? "bg-primary"
                      : "bg-background/50"
                  }`}
                  onClick={() => setCurrentImageIndex(index)}
                  aria-label={`View image ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* Monthly installment badge */}
          <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium">
            From{" "}
            {formatPrice(calculateMinimumMonthlyInstallment(product.price))}
            /month
          </div>

          <div className="absolute right-4 top-4 flex gap-2">
            <Button
              variant="secondary"
              size="icon"
              className="h-10 w-10 rounded-full bg-background/80 backdrop-blur-sm"
              aria-label="Share product"
            >
              <Share className="h-5 w-5" />
            </Button>
            <WishlistButton
              productId={product.id}
              variant="secondary"
              size="icon"
              className="h-10 w-10 rounded-full bg-background/80 backdrop-blur-sm"
            />
          </div>
        </div>

        <div className="px-4">
          {/* Product Name & Price */}
          <h1 className="text-2xl font-bold">{product.name}</h1>
          <div className="flex items-center justify-between mt-2">
            <span className="text-xl font-semibold">
              {formatPrice(product.price)}
            </span>
            <div className="flex items-center">
              <StarIcon className="h-5 w-5 fill-primary text-primary" />
              <span className="text-sm font-medium ml-1">{product.rating}</span>
              <span className="text-xs text-muted-foreground ml-1">
                ({product.reviews} reviews)
              </span>
            </div>
          </div>

          {/* Merchant */}
          <div className="text-sm text-muted-foreground mt-1">
            By <span className="font-medium">{product.merchant.name}</span>
          </div>

          {/* Monthly Installment */}
          <div className="flex items-center text-sm mt-3 text-muted-foreground">
            <Clock className="h-4 w-4 mr-1" />
            <span>
              From{" "}
              {formatPrice(calculateMinimumMonthlyInstallment(product.price))}
              /month over 36 months
            </span>
          </div>

          {/* Separator */}
          <Separator className="my-4" />

          {/* Quantity */}
          <div className="mb-6">
            <label className="text-sm font-medium mb-2 block">Quantity</label>
            <div className="flex items-center">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-r-none"
                onClick={decrementQuantity}
                aria-label="Decrease quantity"
                disabled={quantity <= 1}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <div className="h-8 px-3 flex items-center justify-center border-y border-input">
                {quantity}
              </div>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-l-none"
                onClick={incrementQuantity}
                aria-label="Increase quantity"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Sticky Payment Section */}
          <div className="fixed bottom-16 left-0 right-0 z-40 bg-white border-t p-4">
            <div className="max-w-md mx-auto flex items-center gap-3">
              <WishlistButton
                productId={product.id}
                variant="outline"
                size="lg"
                className="flex-shrink-0"
                showText={false}
              />
              <PurchaseJourneyDrawer product={product} quantity={quantity}>
                <Button className="flex-1" size="lg" variant="default">
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Select Payment Options
                </Button>
              </PurchaseJourneyDrawer>
            </div>
          </div>

          {/* Add bottom padding to prevent content overlap */}
          <div className="h-20"></div>

          {/* Product Information Tabs */}
          <Tabs defaultValue="description" className="w-full mt-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="shipping">Shipping</TabsTrigger>
            </TabsList>
            <TabsContent value="description" className="py-4">
              <p className="text-sm text-muted-foreground leading-relaxed">
                {product.description}
              </p>
              <p className="text-sm text-muted-foreground leading-relaxed mt-3">
                This is a custom-manufactured product created specifically for
                your group purchase. Manufacturing will begin once the group
                commitment is complete.
              </p>
            </TabsContent>
            <TabsContent value="specifications" className="py-4">
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Category</div>
                  <div>{product.category}</div>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Payment Options</div>
                  <div>Cash or Monthly Installments</div>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Min. Monthly</div>
                  <div>
                    {formatPrice(
                      calculateMinimumMonthlyInstallment(product.price)
                    )}
                    /month
                  </div>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Merchant</div>
                  <div>{product.merchant.name}</div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="shipping" className="py-4">
              <div className="space-y-4 text-sm text-muted-foreground">
                <div className="flex items-start">
                  <Truck className="h-5 w-5 mr-2 mt-0.5" />
                  <div>
                    <p className="text-foreground font-medium">
                      Shipping Information
                    </p>
                    <p className="mt-1">
                      Shipping estimates will be calculated based on the
                      manufacturing completion date. The product will be shipped
                      to all group members once manufacturing is complete.
                    </p>
                  </div>
                </div>

                <div className="bg-muted p-3 rounded-md mt-4">
                  <p className="font-medium text-foreground mb-1">
                    Payment Options
                  </p>
                  <ul className="space-y-1 list-disc list-inside">
                    <li>Cash Payment: Full amount upfront</li>
                    <li>
                      Monthly Installments: From{" "}
                      {formatPrice(
                        calculateMinimumMonthlyInstallment(product.price)
                      )}
                      /month over 36 months
                    </li>
                    <li>Group Purchase: Split costs with others</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MobileLayout>
  );
}
