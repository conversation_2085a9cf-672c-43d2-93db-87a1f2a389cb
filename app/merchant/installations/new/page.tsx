"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  Clock,
  Users,
} from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { InstallationTask, TeamMember, GroupPurchase } from "@/types/merchant";
import { formatZAR, convertToZAR } from "@/data/south-african-context";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data - only groups with 100% payment and completed manufacturing
const mockCompletedGroups: GroupPurchase[] = [
  {
    id: "g1",
    productId: "p1",
    merchantId: "m1",
    groupName: "Memorial for John Smith",
    adminId: "u1",
    totalMembers: 8,
    targetAmount: convertToZAR(3200),
    currentAmount: convertToZAR(3200),
    paymentProgress: 100,
    status: "ready_for_installation",
    createdAt: new Date("2024-01-15"),
    manufacturingStarted: new Date("2024-01-25"),
    manufacturingCompleted: new Date("2024-02-15"),
  },
  {
    id: "g2",
    productId: "p2",
    merchantId: "m1",
    groupName: "Family Heritage Stone",
    adminId: "u2",
    totalMembers: 12,
    targetAmount: convertToZAR(4500),
    currentAmount: convertToZAR(4500),
    paymentProgress: 100,
    status: "ready_for_installation",
    createdAt: new Date("2024-01-10"),
    manufacturingStarted: new Date("2024-01-20"),
    manufacturingCompleted: new Date("2024-02-10"),
  },
  {
    id: "g3",
    productId: "p3",
    merchantId: "m1",
    groupName: "Custom Memorial Design",
    adminId: "u3",
    totalMembers: 6,
    targetAmount: convertToZAR(2400),
    currentAmount: convertToZAR(2400),
    paymentProgress: 100,
    status: "ready_for_installation",
    createdAt: new Date("2024-01-20"),
    manufacturingStarted: new Date("2024-02-01"),
    manufacturingCompleted: new Date("2024-02-20"),
  },
];

const mockTeamMembers: TeamMember[] = [
  {
    id: "t1",
    merchantId: "m1",
    name: "John Smith",
    email: "<EMAIL>",
    phone: "+27 82 123 4567",
    role: "manager",
    specialties: ["Stone Installation", "Foundation Work"],
    isActive: true,
    joinedAt: new Date(),
    isVerified: true,
  },
  {
    id: "t2",
    merchantId: "m1",
    name: "Mike Johnson",
    email: "<EMAIL>",
    phone: "+27 82 123 4567",
    role: "contractor",
    specialties: ["Stone Installation"],
    isActive: true,
    joinedAt: new Date(),
    isVerified: true,
  },
  {
    id: "t3",
    merchantId: "m1",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    phone: "+27 82 123 4567",
    role: "employee",
    specialties: ["General Labor"],
    isActive: true,
    joinedAt: new Date(),
    isVerified: true,
  },
];

export default function CreateInstallation() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState("");
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [installationAddress, setInstallationAddress] = useState("");
  const [scheduledDate, setScheduledDate] = useState<Date>();
  const [estimatedDuration, setEstimatedDuration] = useState("");
  const [selectedTeam, setSelectedTeam] = useState<string[]>([]);
  const [teamLead, setTeamLead] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Mock API call - replace with actual implementation
      await new Promise((resolve) => setTimeout(resolve, 1000));
      router.push("/merchant/installations");
      router.refresh();
    } catch (error) {
      console.error("Failed to create installation:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Schedule Installation"
        description="Create a new installation task and assign team members"
        backHref="/merchant/installations"
      />

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Installation Details</CardTitle>
            <CardDescription>
              Provide information about the installation task
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Group Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Group</label>
              <Select value={selectedGroup} onValueChange={setSelectedGroup}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a group ready for installation" />
                </SelectTrigger>
                <SelectContent>
                  {mockCompletedGroups.map((group) => (
                    <SelectItem key={group.id} value={group.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{group.groupName}</span>
                        <span className="text-sm text-muted-foreground">
                          {group.totalMembers} members •{" "}
                          {formatZAR(group.currentAmount)} • Manufacturing
                          completed
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {mockCompletedGroups.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  No groups are ready for installation. Groups must have 100%
                  payment and completed manufacturing.
                </p>
              )}
            </div>

            {/* Title */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Installation Title</label>
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="e.g., Memorial Stone Installation - Oak Hill Cemetery"
              />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Provide details about the installation..."
                className="min-h-[100px]"
              />
            </div>

            {/* Address */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Installation Address
              </label>
              <Input
                value={installationAddress}
                onChange={(e) => setInstallationAddress(e.target.value)}
                placeholder="e.g., Oak Hill Cemetery, Section B, Plot 45"
              />
            </div>

            {/* Date and Duration */}
            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <label className="text-sm font-medium">Installation Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !scheduledDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {scheduledDate
                        ? format(scheduledDate, "PPP")
                        : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={scheduledDate}
                      onSelect={setScheduledDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Estimated Duration (hours)
                </label>
                <Input
                  type="number"
                  min="1"
                  value={estimatedDuration}
                  onChange={(e) => setEstimatedDuration(e.target.value)}
                  placeholder="e.g., 4"
                />
              </div>
            </div>

            {/* Team Assignment */}
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Team Lead</label>
                <Select value={teamLead} onValueChange={setTeamLead}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select team lead" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockTeamMembers
                      .filter((member) => member.role.includes("Lead"))
                      .map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          {member.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Team Members</label>
                <Select
                  value={selectedTeam[selectedTeam.length - 1] || ""}
                  onValueChange={(value) => {
                    if (!selectedTeam.includes(value)) {
                      setSelectedTeam([...selectedTeam, value]);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Add team member" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockTeamMembers
                      .filter((member) => !selectedTeam.includes(member.id))
                      .map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          {member.name} - {member.role}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>

                {/* Selected Team Members */}
                {selectedTeam.length > 0 && (
                  <div className="mt-4 space-y-2">
                    {selectedTeam.map((memberId) => {
                      const member = mockTeamMembers.find(
                        (m) => m.id === memberId
                      );
                      if (!member) return null;
                      return (
                        <div
                          key={member.id}
                          className="flex items-center justify-between p-2 border rounded-md"
                        >
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span>{member.name}</span>
                            <span className="text-sm text-muted-foreground">
                              {member.role}
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              setSelectedTeam(
                                selectedTeam.filter((id) => id !== member.id)
                              )
                            }
                          >
                            Remove
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              isSubmitting ||
              !selectedGroup ||
              !title ||
              !description ||
              !installationAddress ||
              !scheduledDate ||
              !estimatedDuration ||
              !teamLead ||
              selectedTeam.length === 0
            }
          >
            {isSubmitting ? "Creating..." : "Create Installation"}
          </Button>
        </div>
      </form>
    </div>
  );
}
