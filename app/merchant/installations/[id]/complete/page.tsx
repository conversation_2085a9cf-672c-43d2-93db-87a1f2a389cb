"use client";

import { useState } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Upload, X, Image as ImageIcon } from "lucide-react";
import { InstallationTask } from "@/types/merchant";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

export default function CompleteInstallation() {
  const router = useRouter();
  const { id } = useParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [completionNotes, setCompletionNotes] = useState("");

  // Mock data - replace with API call
  const mockInstallation: InstallationTask = {
    id: "inst1",
    groupId: "g1",
    productId: "p1",
    merchantId: "m1",
    title: "Memorial Stone Installation - Oak Hill Cemetery",
    description: "Installation of granite memorial stone",
    installationAddress: "Oak Hill Cemetery, Section B, Plot 45",
    scheduledDate: new Date("2024-02-15"),
    estimatedDuration: 4,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "in_progress",
    paymentReleased: false,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setSelectedFiles((prev) => [...prev, ...files]);
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Mock API call - replace with actual implementation
      await new Promise((resolve) => setTimeout(resolve, 1000));
      router.push(`/merchant/installations/${id}`);
      router.refresh();
    } catch (error) {
      console.error("Failed to complete installation:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <MerchantPageHeader
        title={mockInstallation.title}
        description={mockInstallation.description}
        backHref={`/merchant/installations/${id}`}
      />

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Installation Details */}
        <Card>
          <CardHeader>
            <CardTitle>{mockInstallation.title}</CardTitle>
            <CardDescription>{mockInstallation.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>
                <span className="font-medium">Location:</span>{" "}
                {mockInstallation.installationAddress}
              </p>
              <p>
                <span className="font-medium">Scheduled Date:</span>{" "}
                {mockInstallation.scheduledDate.toLocaleDateString("en-ZA", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
              <p>
                <span className="font-medium">Estimated Duration:</span>{" "}
                {mockInstallation.estimatedDuration} hours
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Completion Form */}
        <Card>
          <CardHeader>
            <CardTitle>Completion Details</CardTitle>
            <CardDescription>
              Provide completion notes and upload photos
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Completion Notes */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Completion Notes</label>
              <Textarea
                value={completionNotes}
                onChange={(e) => setCompletionNotes(e.target.value)}
                placeholder="Describe any important details about the installation completion..."
                className="min-h-[120px]"
              />
            </div>

            {/* Photo Upload */}
            <div className="space-y-4">
              <label className="text-sm font-medium">Completion Photos</label>

              {/* File List */}
              {selectedFiles.length > 0 && (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="relative aspect-square border rounded-lg flex items-center justify-center bg-muted"
                    >
                      <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-1 right-1 h-6 w-6"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {/* Upload Button */}
              <div className="flex items-center justify-center w-full">
                <label className="w-full cursor-pointer">
                  <div className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg hover:bg-muted/50 transition-colors">
                    <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Click to upload completion photos
                    </p>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    multiple
                    onChange={handleFileSelect}
                  />
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              isSubmitting || !completionNotes || selectedFiles.length === 0
            }
          >
            {isSubmitting ? "Submitting..." : "Mark as Complete"}
          </Button>
        </div>
      </form>
    </div>
  );
}
