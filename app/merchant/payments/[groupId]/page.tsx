"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DollarSign,
  Users,
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  Download,
  Mail,
  Phone,
} from "lucide-react";
import { PaymentTracking, GroupPurchase } from "@/types/merchant";
import { formatZAR, convertToZAR } from "@/data/south-african-context";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data - replace with actual API calls
const mockGroup: GroupPurchase = {
  id: "g1",
  productId: "p1",
  merchantId: "m1",
  groupName: "Memorial for John Smith",
  adminId: "u1",
  totalMembers: 8,
  targetAmount: convertToZAR(3200),
  currentAmount: convertToZAR(2880),
  paymentProgress: 90,
  status: "collecting",
  createdAt: new Date("2024-01-15"),
};

const mockPaymentTracking: PaymentTracking = {
  groupId: "g1",
  productId: "p1",
  merchantId: "m1",
  totalAmount: convertToZAR(3200),
  paidAmount: convertToZAR(2880),
  pendingAmount: convertToZAR(320),
  paymentProgress: 90,
  memberPayments: [
    {
      userId: "u1",
      userName: "Alice Johnson (Admin)",
      shareAmount: convertToZAR(400),
      paidAmount: convertToZAR(400),
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-20"),
    },
    {
      userId: "u2",
      userName: "Bob Smith",
      shareAmount: convertToZAR(400),
      paidAmount: convertToZAR(400),
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-18"),
    },
    {
      userId: "u3",
      userName: "Carol Davis",
      shareAmount: convertToZAR(400),
      paidAmount: convertToZAR(320),
      paymentStatus: "partial",
      lastPaymentDate: new Date("2024-01-22"),
    },
    {
      userId: "u4",
      userName: "David Wilson",
      shareAmount: convertToZAR(400),
      paidAmount: convertToZAR(400),
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-19"),
    },
    {
      userId: "u5",
      userName: "Eva Brown",
      shareAmount: convertToZAR(400),
      paidAmount: convertToZAR(400),
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-21"),
    },
    {
      userId: "u6",
      userName: "Frank Miller",
      shareAmount: convertToZAR(400),
      paidAmount: convertToZAR(400),
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-17"),
    },
    {
      userId: "u7",
      userName: "Grace Taylor",
      shareAmount: convertToZAR(400),
      paidAmount: convertToZAR(360),
      paymentStatus: "partial",
      lastPaymentDate: new Date("2024-01-23"),
    },
    {
      userId: "u8",
      userName: "Henry Anderson",
      shareAmount: convertToZAR(400),
      paidAmount: 0,
      paymentStatus: "pending",
    },
  ],
  readyForManufacturing: true,
  manufacturingThreshold: 80,
};

const mockPaymentHistory = [
  {
    id: "pay1",
    userId: "u1",
    userName: "Alice Johnson",
    amount: convertToZAR(400),
    date: new Date("2024-01-20"),
    method: "Credit Card",
    status: "completed",
  },
  {
    id: "pay2",
    userId: "u2",
    userName: "Bob Smith",
    amount: convertToZAR(400),
    date: new Date("2024-01-18"),
    method: "Bank Transfer",
    status: "completed",
  },
  {
    id: "pay3",
    userId: "u3",
    userName: "Carol Davis",
    amount: convertToZAR(200),
    date: new Date("2024-01-15"),
    method: "Credit Card",
    status: "completed",
  },
  {
    id: "pay4",
    userId: "u3",
    userName: "Carol Davis",
    amount: convertToZAR(120),
    date: new Date("2024-01-22"),
    method: "Credit Card",
    status: "completed",
  },
];

export default function GroupPaymentDetails() {
  const params = useParams();
  const groupId = params.groupId as string;

  const [group, setGroup] = useState<GroupPurchase>(mockGroup);
  const [paymentTracking, setPaymentTracking] =
    useState<PaymentTracking>(mockPaymentTracking);
  const [paymentHistory, setPaymentHistory] = useState(mockPaymentHistory);

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600";
      case "partial":
        return "text-yellow-600";
      case "pending":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "partial":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "pending":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const completedPayments = paymentTracking.memberPayments.filter(
    (m) => m.paymentStatus === "completed"
  ).length;
  const partialPayments = paymentTracking.memberPayments.filter(
    (m) => m.paymentStatus === "partial"
  ).length;
  const pendingPayments = paymentTracking.memberPayments.filter(
    (m) => m.paymentStatus === "pending"
  ).length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title={group.groupName}
        description="Payment tracking and member details"
        backHref="/merchant/payments"
      />

      {/* Payment Summary */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Collected
            </CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatZAR(paymentTracking.paidAmount)}
            </div>
            <p className="text-xs text-muted-foreground">
              of {formatZAR(paymentTracking.totalAmount)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Payment Progress
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {paymentTracking.paymentProgress}%
            </div>
            <Progress
              value={paymentTracking.paymentProgress}
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Pending Amount
            </CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatZAR(paymentTracking.pendingAmount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {pendingPayments + partialPayments} members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Manufacturing Ready
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {paymentTracking.readyForManufacturing ? "Yes" : "No"}
            </div>
            <p className="text-xs text-muted-foreground">
              {paymentTracking.manufacturingThreshold}% threshold
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Ready for Manufacturing Alert */}
      {paymentTracking.readyForManufacturing && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="font-medium text-orange-800">
                  Ready for Manufacturing
                </p>
                <p className="text-sm text-orange-700">
                  This group has reached the{" "}
                  {paymentTracking.manufacturingThreshold}% payment threshold
                  and is ready to begin manufacturing.
                </p>
              </div>
              <Link href={`/merchant/manufacturing/${groupId}`}>
                <Button className="ml-auto">Start Manufacturing</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="members" className="space-y-4">
        <TabsList>
          <TabsTrigger value="members">Member Payments</TabsTrigger>
          <TabsTrigger value="history">Payment History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="members" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Member Payment Status</CardTitle>
              <CardDescription>
                Individual payment progress for each group member
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentTracking.memberPayments.map((member) => (
                  <div
                    key={member.userId}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarFallback>
                          {member.userName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{member.userName}</p>
                        <p className="text-sm text-muted-foreground">
                          Share: {formatZAR(member.shareAmount)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <p className="font-medium">
                          {formatZAR(member.paidAmount)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {member.lastPaymentDate
                            ? `Last: ${member.lastPaymentDate.toLocaleDateString(
                                "en-ZA"
                              )}`
                            : "No payments"}
                        </p>
                      </div>

                      <div className="w-24">
                        <Progress
                          value={(member.paidAmount / member.shareAmount) * 100}
                          className="h-2"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        {getPaymentStatusIcon(member.paymentStatus)}
                        <span
                          className={`text-sm font-medium ${getPaymentStatusColor(
                            member.paymentStatus
                          )}`}
                        >
                          {member.paymentStatus}
                        </span>
                      </div>

                      <div className="flex space-x-1">
                        <Link
                          href={`/merchant/payments/member/${member.userId}`}
                        >
                          <Button variant="outline" size="sm">
                            View History
                          </Button>
                        </Link>
                        <Button variant="ghost" size="sm">
                          <Mail className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Phone className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-medium">Completed</span>
                  </div>
                  <p className="text-2xl font-bold">{completedPayments}</p>
                  <p className="text-sm text-muted-foreground">members</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <Clock className="h-5 w-5 text-yellow-600" />
                    <span className="font-medium">Partial</span>
                  </div>
                  <p className="text-2xl font-bold">{partialPayments}</p>
                  <p className="text-sm text-muted-foreground">members</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <AlertCircle className="h-5 w-5 text-red-600" />
                    <span className="font-medium">Pending</span>
                  </div>
                  <p className="text-2xl font-bold">{pendingPayments}</p>
                  <p className="text-sm text-muted-foreground">members</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Payment History</CardTitle>
                  <CardDescription>
                    Chronological list of all payments received
                  </CardDescription>
                </div>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentHistory.map((payment) => (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarFallback>
                          {payment.userName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{payment.userName}</p>
                        <p className="text-sm text-muted-foreground">
                          {payment.method} •{" "}
                          {payment.date.toLocaleDateString("en-ZA")}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <p className="font-medium">{formatZAR(payment.amount)}</p>
                      <Badge className="bg-green-100 text-green-800">
                        {payment.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Analytics</CardTitle>
              <CardDescription>Payment trends and insights</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Payment analytics dashboard coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
