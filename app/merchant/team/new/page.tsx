"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronLeft, Save, Upload, X, Plus, UserPlus } from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

export default function AddTeamMember() {
  const router = useRouter();
  const [selectedPhoto, setSelectedPhoto] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string>("");
  const [selectedSpecialties, setSelectedSpecialties] = useState<string[]>([]);
  const [newSpecialty, setNewSpecialty] = useState("");

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    role: "contractor" as
      | "contractor"
      | "employee"
      | "manager"
      | "factory_worker"
      | "factory_manager",
    isActive: true,
    isVerified: true,
  });

  const [bankingDetails, setBankingDetails] = useState({
    accountNumber: "",
    routingNumber: "",
    bankName: "",
    accountHolderName: "",
  });

  const [identification, setIdentification] = useState({
    idType: "drivers_license" as "drivers_license" | "passport" | "national_id",
    idNumber: "",
  });

  const availableSpecialties = [
    "Stone Installation",
    "Foundation Work",
    "Heavy Equipment",
    "Site Preparation",
    "Monument Installation",
    "Landscaping",
    "Equipment Operation",
    "Quality Control",
    "Engraving",
    "Detail Work",
    "Custom Installations",
    "Memorial Restoration",
    "Cleaning",
    "Maintenance",
    "Project Management",
    "Quality Assurance",
    "Team Leadership",
  ];

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedPhoto(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removePhoto = () => {
    setSelectedPhoto(null);
    setPhotoPreview("");
  };

  const addSpecialty = (specialty: string) => {
    if (!selectedSpecialties.includes(specialty)) {
      setSelectedSpecialties([...selectedSpecialties, specialty]);
    }
  };

  const addCustomSpecialty = () => {
    if (newSpecialty && !selectedSpecialties.includes(newSpecialty)) {
      setSelectedSpecialties([...selectedSpecialties, newSpecialty]);
      setNewSpecialty("");
    }
  };

  const removeSpecialty = (specialty: string) => {
    setSelectedSpecialties(selectedSpecialties.filter((s) => s !== specialty));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement actual team member creation
    console.log("Creating team member:", {
      ...formData,
      specialties: selectedSpecialties,
      photo: selectedPhoto,
      bankingDetails:
        formData.role === "contractor" ? bankingDetails : undefined,
      identification:
        formData.role === "contractor" ? identification : undefined,
    });
    router.push("/merchant/team");
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Add Team Member"
        description="Add a new team member or contractor"
        backHref="/merchant/team"
      />

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Personal details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Photo Upload */}
              <div className="space-y-2">
                <Label>Profile Photo</Label>
                <div className="flex items-center space-x-4">
                  <Avatar className="h-20 w-20">
                    {photoPreview ? (
                      <AvatarImage src={photoPreview} alt="Profile preview" />
                    ) : (
                      <AvatarFallback>
                        <UserPlus className="h-8 w-8" />
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div className="space-y-2">
                    <div>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handlePhotoUpload}
                        className="hidden"
                        id="photo-upload"
                      />
                      <Button type="button" variant="outline" asChild>
                        <label
                          htmlFor="photo-upload"
                          className="cursor-pointer"
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Upload Photo
                        </label>
                      </Button>
                    </div>
                    {photoPreview && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={removePhoto}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Remove
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="John Doe"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+27 82 123 4567"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select
                  value={formData.role}
                  onValueChange={(value) => handleInputChange("role", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="contractor">Contractor</SelectItem>
                    <SelectItem value="employee">Employee</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="factory_worker">
                      Factory Worker
                    </SelectItem>
                    <SelectItem value="factory_manager">
                      Factory Manager
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Specialties */}
          <Card>
            <CardHeader>
              <CardTitle>Specialties & Skills</CardTitle>
              <CardDescription>
                Areas of expertise and specialization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Available Specialties */}
              <div className="space-y-2">
                <Label>Available Specialties</Label>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {availableSpecialties
                    .filter(
                      (specialty) => !selectedSpecialties.includes(specialty)
                    )
                    .map((specialty) => (
                      <Button
                        key={specialty}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addSpecialty(specialty)}
                      >
                        <Plus className="mr-1 h-3 w-3" />
                        {specialty}
                      </Button>
                    ))}
                </div>
              </div>

              {/* Custom Specialty */}
              <div className="space-y-2">
                <Label>Add Custom Specialty</Label>
                <div className="flex gap-2">
                  <Input
                    value={newSpecialty}
                    onChange={(e) => setNewSpecialty(e.target.value)}
                    placeholder="Enter custom specialty"
                  />
                  <Button
                    type="button"
                    onClick={addCustomSpecialty}
                    disabled={!newSpecialty}
                  >
                    Add
                  </Button>
                </div>
              </div>

              {/* Selected Specialties */}
              {selectedSpecialties.length > 0 && (
                <div className="space-y-2">
                  <Label>
                    Selected Specialties ({selectedSpecialties.length})
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {selectedSpecialties.map((specialty) => (
                      <Badge
                        key={specialty}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {specialty}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeSpecialty(specialty)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Contractor-specific fields */}
        {formData.role === "contractor" && (
          <div className="grid gap-8 lg:grid-cols-2">
            {/* Banking Details */}
            <Card>
              <CardHeader>
                <CardTitle>Banking Information</CardTitle>
                <CardDescription>
                  Payment details for contractor compensation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="bankName">Bank Name</Label>
                  <Input
                    id="bankName"
                    value={bankingDetails.bankName}
                    onChange={(e) =>
                      setBankingDetails((prev) => ({
                        ...prev,
                        bankName: e.target.value,
                      }))
                    }
                    placeholder="First National Bank"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="accountHolderName">Account Holder Name</Label>
                  <Input
                    id="accountHolderName"
                    value={bankingDetails.accountHolderName}
                    onChange={(e) =>
                      setBankingDetails((prev) => ({
                        ...prev,
                        accountHolderName: e.target.value,
                      }))
                    }
                    placeholder="John Doe"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="accountNumber">Account Number</Label>
                  <Input
                    id="accountNumber"
                    value={bankingDetails.accountNumber}
                    onChange={(e) =>
                      setBankingDetails((prev) => ({
                        ...prev,
                        accountNumber: e.target.value,
                      }))
                    }
                    placeholder="**********"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="routingNumber">Routing Number</Label>
                  <Input
                    id="routingNumber"
                    value={bankingDetails.routingNumber}
                    onChange={(e) =>
                      setBankingDetails((prev) => ({
                        ...prev,
                        routingNumber: e.target.value,
                      }))
                    }
                    placeholder="*********"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Identification */}
            <Card>
              <CardHeader>
                <CardTitle>Identification</CardTitle>
                <CardDescription>
                  Identity verification information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="idType">ID Type</Label>
                  <Select
                    value={identification.idType}
                    onValueChange={(value) =>
                      setIdentification((prev) => ({
                        ...prev,
                        idType: value as any,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="drivers_license">
                        Driver's License
                      </SelectItem>
                      <SelectItem value="passport">Passport</SelectItem>
                      <SelectItem value="national_id">National ID</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="idNumber">ID Number</Label>
                  <Input
                    id="idNumber"
                    value={identification.idNumber}
                    onChange={(e) =>
                      setIdentification((prev) => ({
                        ...prev,
                        idNumber: e.target.value,
                      }))
                    }
                    placeholder="DL123456789"
                  />
                </div>

                <div className="space-y-2">
                  <Label>ID Document Upload</Label>
                  <div>
                    <input
                      type="file"
                      accept="image/*,.pdf"
                      className="hidden"
                      id="id-upload"
                    />
                    <Button type="button" variant="outline" asChild>
                      <label htmlFor="id-upload" className="cursor-pointer">
                        <Upload className="mr-2 h-4 w-4" />
                        Upload ID Document
                      </label>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Link href="/merchant/team">
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </Link>
          <Button type="submit">
            <Save className="mr-2 h-4 w-4" />
            Add Team Member
          </Button>
        </div>
      </form>
    </div>
  );
}
