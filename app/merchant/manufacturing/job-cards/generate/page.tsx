"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Calendar,
  Users,
  Package,
  AlertCircle,
  Save,
  ArrowLeft,
  Edit,
} from "lucide-react";
import {
  GroupPurchase,
  ManufacturingPhase,
  TeamMember,
  RawMaterial,
  JobCard,
  JobCardPhase,
  JobCardMaterial,
} from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data
const mockGroup: GroupPurchase = {
  id: "g1",
  productId: "p1",
  merchantId: "m1",
  groupName: "Memorial for John Smith",
  adminId: "u1",
  totalMembers: 8,
  targetAmount: convertToZAR(3200),
  currentAmount: convertToZAR(2880),
  paymentProgress: 90,
  status: "collecting",
  createdAt: new Date("2024-01-15"),
};

// Mock product with material requirements
const mockProduct = {
  id: "p1",
  name: "Classic Granite Memorial",
  materialRequirements: [
    {
      materialId: "rm1",
      materialName: "Premium Black Granite",
      requiredQuantity: 2.5,
      unit: "m²",
      isOptional: false,
      notes: "Main stone material",
    },
    {
      materialId: "rm2",
      materialName: "Engraving Tools",
      requiredQuantity: 1,
      unit: "set",
      isOptional: false,
      notes: "For custom engraving work",
    },
    {
      materialId: "rm3",
      materialName: "Polish Compound",
      requiredQuantity: 0.5,
      unit: "kg",
      isOptional: true,
      notes: "For premium finish",
    },
  ],
};

const mockPhases: ManufacturingPhase[] = [
  {
    id: "ph1",
    productId: "p1",
    merchantId: "m1",
    name: "Design Approval",
    description: "Customer approves final design and specifications",
    estimatedDuration: 2,
    order: 1,
    isRequired: true,
    isGlobal: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "ph2",
    productId: "p1",
    merchantId: "m1",
    name: "Stone Cutting",
    description: "Cut granite to specifications using CNC machine",
    estimatedDuration: 5,
    order: 2,
    isRequired: true,
    isGlobal: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "ph3",
    productId: "p1",
    merchantId: "m1",
    name: "Engraving",
    description: "Engrave memorial text and designs",
    estimatedDuration: 3,
    order: 3,
    isRequired: true,
    isGlobal: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "ph4",
    productId: "p1",
    merchantId: "m1",
    name: "Final Polish",
    description: "Final polish and quality check",
    estimatedDuration: 2,
    order: 4,
    isRequired: true,
    isGlobal: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockTeamMembers: TeamMember[] = [
  {
    id: "t1",
    merchantId: "m1",
    name: "Thabo Mthembu",
    email: "<EMAIL>",
    phone: "+27 82 123 4567",
    role: "factory_manager",
    specialties: ["Stone Cutting", "CNC Operation", "Quality Control"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-06-01"),
  },
  {
    id: "t2",
    merchantId: "m1",
    name: "Sarah van der Merwe",
    email: "<EMAIL>",
    phone: "+27 83 234 5678",
    role: "factory_worker",
    specialties: ["Stone Cutting", "Polishing", "General Labor"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-08-15"),
  },
  {
    id: "t3",
    merchantId: "m1",
    name: "Johan Pretorius",
    email: "<EMAIL>",
    phone: "+27 84 345 6789",
    role: "factory_worker",
    specialties: ["Engraving", "Detail Work", "Artistic Design"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-09-01"),
  },
];

const mockMaterials: RawMaterial[] = [
  {
    id: "rm1",
    merchantId: "m1",
    name: "Premium Black Granite",
    description: "High-quality black granite from Mpumalanga quarries",
    category: "stone",
    unit: "m²",
    costPerUnit: convertToZAR(450),
    currentStock: 25.5,
    minimumStock: 10.0,
    maximumStock: 100.0,
    supplierId: "sup1",
    specifications: {},
    storageLocation: "Warehouse A - Section 1",
    isActive: true,
    lastRestocked: new Date("2024-01-15"),
    createdAt: new Date("2023-06-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "rm2",
    merchantId: "m1",
    name: "Engraving Tools",
    description: "Professional engraving tool set",
    category: "tools",
    unit: "set",
    costPerUnit: convertToZAR(150),
    currentStock: 5,
    minimumStock: 2,
    maximumStock: 10,
    supplierId: "sup2",
    specifications: {},
    storageLocation: "Tool Room",
    isActive: true,
    lastRestocked: new Date("2024-01-10"),
    createdAt: new Date("2023-06-01"),
    updatedAt: new Date("2024-01-10"),
  },
];

export default function GenerateJobCard() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const groupId = searchParams.get("groupId");

  const [group] = useState<GroupPurchase>(mockGroup);
  const [phases] = useState<ManufacturingPhase[]>(mockPhases);
  const [teamMembers] = useState<TeamMember[]>(mockTeamMembers);
  const [materials] = useState<RawMaterial[]>(mockMaterials);
  const [product] = useState(mockProduct);

  const [formData, setFormData] = useState({
    jobNumber: `JOB-${new Date().getFullYear()}-${String(Date.now()).slice(
      -3
    )}`,
    priority: "medium" as "low" | "medium" | "high" | "urgent",
    estimatedStartDate: new Date().toISOString().split("T")[0],
    estimatedCompletionDate: "",
    teamLeadId: "",
    assignedTeam: [] as string[],
    notes: "",
  });

  const [selectedPhases, setSelectedPhases] = useState<string[]>(
    phases.map((phase) => phase.id)
  );
  const [phaseAssignments, setPhaseAssignments] = useState<
    Record<string, string[]>
  >({});

  // Initialize material requirements from product definition
  const [materialRequirements, setMaterialRequirements] = useState<
    Array<{ materialId: string; quantity: number }>
  >(
    product.materialRequirements.map((req) => ({
      materialId: req.materialId,
      quantity: req.requiredQuantity,
    }))
  );

  // Calculate estimated completion date based on phases
  useEffect(() => {
    if (formData.estimatedStartDate && selectedPhases.length > 0) {
      const startDate = new Date(formData.estimatedStartDate);
      const totalDuration = selectedPhases.reduce((sum, phaseId) => {
        const phase = phases.find((p) => p.id === phaseId);
        return sum + (phase?.estimatedDuration || 0);
      }, 0);

      const completionDate = new Date(startDate);
      completionDate.setDate(completionDate.getDate() + totalDuration);

      setFormData((prev) => ({
        ...prev,
        estimatedCompletionDate: completionDate.toISOString().split("T")[0],
      }));
    }
  }, [formData.estimatedStartDate, selectedPhases, phases]);

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleTeamMemberToggle = (memberId: string) => {
    setFormData((prev) => ({
      ...prev,
      assignedTeam: prev.assignedTeam.includes(memberId)
        ? prev.assignedTeam.filter((id) => id !== memberId)
        : [...prev.assignedTeam, memberId],
    }));
  };

  const handlePhaseToggle = (phaseId: string) => {
    setSelectedPhases((prev) =>
      prev.includes(phaseId)
        ? prev.filter((id) => id !== phaseId)
        : [...prev, phaseId]
    );
  };

  const handlePhaseWorkerToggle = (phaseId: string, workerId: string) => {
    setPhaseAssignments((prev) => ({
      ...prev,
      [phaseId]: prev[phaseId]?.includes(workerId)
        ? prev[phaseId].filter((id) => id !== workerId)
        : [...(prev[phaseId] || []), workerId],
    }));
  };

  const handleMaterialQuantityChange = (
    materialId: string,
    quantity: number
  ) => {
    setMaterialRequirements((prev) => {
      const existing = prev.find((req) => req.materialId === materialId);
      if (existing) {
        return prev.map((req) =>
          req.materialId === materialId ? { ...req, quantity } : req
        );
      } else {
        return [...prev, { materialId, quantity }];
      }
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Generate job card phases
    const jobCardPhases: JobCardPhase[] = selectedPhases.map(
      (phaseId, index) => {
        const phase = phases.find((p) => p.id === phaseId)!;
        return {
          id: `jcp${index + 1}`,
          phaseId: phase.id,
          phaseName: phase.name,
          description: phase.description,
          estimatedDuration: phase.estimatedDuration,
          status: "pending" as const,
          assignedWorkers: phaseAssignments[phaseId] || [],
          order: index + 1,
        };
      }
    );

    // Generate material requirements
    const jobCardMaterials: JobCardMaterial[] = materialRequirements
      .filter((req) => req.quantity > 0)
      .map((req, index) => {
        const material = materials.find((m) => m.id === req.materialId)!;
        return {
          id: `jcm${index + 1}`,
          materialId: material.id,
          materialName: material.name,
          requiredQuantity: req.quantity,
          usedQuantity: 0,
          unit: material.unit,
          costPerUnit: material.costPerUnit,
          totalCost: material.costPerUnit * req.quantity,
          status: "pending" as const,
        };
      });

    const jobCard: Partial<JobCard> = {
      jobNumber: formData.jobNumber,
      groupId: group.id,
      productId: group.productId,
      merchantId: group.merchantId,
      groupName: group.groupName,
      productName: "Classic Granite Memorial", // This should come from product data
      productImage: getStoneImagesForCategory("traditional", 1)[0] || "",
      status: "pending",
      priority: formData.priority,
      assignedTeam: formData.assignedTeam,
      teamLeadId: formData.teamLeadId,
      estimatedStartDate: new Date(formData.estimatedStartDate),
      estimatedCompletionDate: new Date(formData.estimatedCompletionDate),
      phases: jobCardPhases,
      materialRequirements: jobCardMaterials,
      notes: formData.notes,
      createdBy: "admin", // This should come from current user
    };

    // TODO: Implement actual job card creation
    console.log("Creating job card:", jobCard);

    // Redirect to job cards list
    router.push("/merchant/manufacturing/job-cards");
  };

  const handleCancel = () => {
    router.push("/merchant/manufacturing/job-cards");
  };

  const totalEstimatedDuration = selectedPhases.reduce((sum, phaseId) => {
    const phase = phases.find((p) => p.id === phaseId);
    return sum + (phase?.estimatedDuration || 0);
  }, 0);

  const totalMaterialCost = materialRequirements.reduce((sum, req) => {
    const material = materials.find((m) => m.id === req.materialId);
    return sum + (material ? material.costPerUnit * req.quantity : 0);
  }, 0);

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Generate Job Card"
        description={`Create a new job card for ${group.groupName}`}
        backHref="/merchant/manufacturing/job-cards"
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              <Save className="mr-2 h-4 w-4" />
              Save Job Card
            </Button>
          </div>
        }
      />

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Job Card Information</CardTitle>
            <CardDescription>
              Basic details for the manufacturing job card
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="jobNumber">Job Number *</Label>
                <Input
                  id="jobNumber"
                  value={formData.jobNumber}
                  onChange={(e) =>
                    handleInputChange("jobNumber", e.target.value)
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priority *</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) =>
                    handleInputChange("priority", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="estimatedStartDate">
                  Estimated Start Date *
                </Label>
                <Input
                  id="estimatedStartDate"
                  type="date"
                  value={formData.estimatedStartDate}
                  onChange={(e) =>
                    handleInputChange("estimatedStartDate", e.target.value)
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="estimatedCompletionDate">
                  Estimated Completion Date
                </Label>
                <Input
                  id="estimatedCompletionDate"
                  type="date"
                  value={formData.estimatedCompletionDate}
                  onChange={(e) =>
                    handleInputChange("estimatedCompletionDate", e.target.value)
                  }
                  readOnly
                />
                <p className="text-xs text-muted-foreground">
                  Calculated based on selected phases ({totalEstimatedDuration}{" "}
                  days)
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes or special instructions..."
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Team Assignment */}
        <Card>
          <CardHeader>
            <CardTitle>Team Assignment</CardTitle>
            <CardDescription>
              Select team members and assign a team lead
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="teamLead">Team Lead *</Label>
              <Select
                value={formData.teamLeadId}
                onValueChange={(value) =>
                  handleInputChange("teamLeadId", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select team lead" />
                </SelectTrigger>
                <SelectContent>
                  {teamMembers
                    .filter(
                      (member) =>
                        member.role === "factory_manager" ||
                        member.role === "manager"
                    )
                    .map((member) => (
                      <SelectItem key={member.id} value={member.id}>
                        {member.name} - {member.role.replace("_", " ")}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Team Members</Label>
              <div className="space-y-2">
                {teamMembers.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center space-x-3 p-3 border rounded-lg"
                  >
                    <Checkbox
                      id={`team-${member.id}`}
                      checked={formData.assignedTeam.includes(member.id)}
                      onCheckedChange={() => handleTeamMemberToggle(member.id)}
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Label
                          htmlFor={`team-${member.id}`}
                          className="font-medium"
                        >
                          {member.name}
                        </Label>
                        <span className="text-sm text-muted-foreground capitalize">
                          {member.role.replace("_", " ")}
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {member.specialties
                          .slice(0, 3)
                          .map((specialty, index) => (
                            <span
                              key={index}
                              className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                            >
                              {specialty}
                            </span>
                          ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Manufacturing Phases */}
        <Card>
          <CardHeader>
            <CardTitle>Manufacturing Phases</CardTitle>
            <CardDescription>
              Select phases and assign workers to each phase
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {phases.map((phase) => (
              <div key={phase.id} className="border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id={`phase-${phase.id}`}
                    checked={selectedPhases.includes(phase.id)}
                    onCheckedChange={() => handlePhaseToggle(phase.id)}
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <Label
                        htmlFor={`phase-${phase.id}`}
                        className="font-medium"
                      >
                        {phase.name}
                      </Label>
                      <span className="text-sm text-muted-foreground">
                        {phase.estimatedDuration} days
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {phase.description}
                    </p>

                    {selectedPhases.includes(phase.id) && (
                      <div className="space-y-2">
                        <Label className="text-sm">Assign Workers</Label>
                        <div className="grid gap-2 md:grid-cols-2">
                          {formData.assignedTeam.map((memberId) => {
                            const member = teamMembers.find(
                              (m) => m.id === memberId
                            );
                            if (!member) return null;

                            return (
                              <div
                                key={memberId}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={`phase-${phase.id}-worker-${memberId}`}
                                  checked={
                                    phaseAssignments[phase.id]?.includes(
                                      memberId
                                    ) || false
                                  }
                                  onCheckedChange={() =>
                                    handlePhaseWorkerToggle(phase.id, memberId)
                                  }
                                />
                                <Label
                                  htmlFor={`phase-${phase.id}-worker-${memberId}`}
                                  className="text-sm"
                                >
                                  {member.name}
                                </Label>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Material Requirements */}
        <Card>
          <CardHeader>
            <CardTitle>Material Requirements</CardTitle>
            <CardDescription>
              Review and adjust material quantities for this job (based on
              product requirements)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {product.materialRequirements.map((productReq) => {
              const material = materials.find(
                (m) => m.id === productReq.materialId
              );
              const currentQuantity =
                materialRequirements.find(
                  (req) => req.materialId === productReq.materialId
                )?.quantity || 0;

              if (!material) return null;

              return (
                <div
                  key={productReq.materialId}
                  className="flex items-center space-x-4 p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{productReq.materialName}</h4>
                      {productReq.isOptional && (
                        <Badge variant="outline">Optional</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Recommended: {productReq.requiredQuantity}{" "}
                      {productReq.unit}
                      {productReq.notes && ` • ${productReq.notes}`}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Available: {material.currentStock} {material.unit} | Cost:{" "}
                      {formatZAR(material.costPerUnit)} per {material.unit}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Label
                      htmlFor={`material-${productReq.materialId}`}
                      className="text-sm"
                    >
                      Quantity:
                    </Label>
                    <Input
                      id={`material-${productReq.materialId}`}
                      type="number"
                      step="0.01"
                      min="0"
                      max={material.currentStock}
                      value={currentQuantity}
                      className="w-24"
                      onChange={(e) =>
                        handleMaterialQuantityChange(
                          productReq.materialId,
                          parseFloat(e.target.value) || 0
                        )
                      }
                    />
                    <span className="text-sm text-muted-foreground">
                      {productReq.unit}
                    </span>
                  </div>
                </div>
              );
            })}

            {totalMaterialCost > 0 && (
              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Material Cost:</span>
                  <span className="font-bold text-lg">
                    {formatZAR(totalMaterialCost)}
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Job Card Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {selectedPhases.length}
                </div>
                <p className="text-sm text-muted-foreground">Phases</p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {formData.assignedTeam.length}
                </div>
                <p className="text-sm text-muted-foreground">Team Members</p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {totalEstimatedDuration}
                </div>
                <p className="text-sm text-muted-foreground">Estimated Days</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
