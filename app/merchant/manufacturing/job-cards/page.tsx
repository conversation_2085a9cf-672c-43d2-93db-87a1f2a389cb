"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Plus,
  FileText,
  Clock,
  Users,
  AlertCircle,
  CheckCircle,
  Pause,
  X,
  Calendar,
  Package,
} from "lucide-react";
import { JobCard } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock job cards data
const mockJobCards: JobCard[] = [
  {
    id: "jc1",
    jobNumber: "JOB-2024-001",
    groupId: "g1",
    productId: "p1",
    merchantId: "m1",
    groupName: "Memorial for <PERSON> Smith",
    productName: "Classic Granite Memorial",
    productImage: getStoneImagesForCategory("traditional", 1)[0] || "",
    status: "in_progress",
    priority: "high",
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    estimatedStartDate: new Date("2024-02-01"),
    actualStartDate: new Date("2024-02-01"),
    estimatedCompletionDate: new Date("2024-02-15"),
    phases: [
      {
        id: "jcp1",
        phaseId: "ph1",
        phaseName: "Design Approval",
        description: "Customer approves final design",
        estimatedDuration: 2,
        actualDuration: 1,
        status: "completed",
        assignedWorkers: ["t1"],
        startDate: new Date("2024-02-01"),
        completionDate: new Date("2024-02-02"),
        order: 1,
      },
      {
        id: "jcp2",
        phaseId: "ph2",
        phaseName: "Stone Cutting",
        description: "Cut granite to specifications",
        estimatedDuration: 5,
        status: "in_progress",
        assignedWorkers: ["t1", "t2"],
        startDate: new Date("2024-02-03"),
        order: 2,
      },
    ],
    materialRequirements: [
      {
        id: "jcm1",
        materialId: "rm1",
        materialName: "Premium Black Granite",
        requiredQuantity: 2.5,
        usedQuantity: 1.2,
        unit: "m²",
        costPerUnit: convertToZAR(450),
        totalCost: convertToZAR(1125),
        status: "allocated",
        allocatedBy: "admin",
        allocatedAt: new Date("2024-02-01"),
      },
    ],
    notes: "High priority job for long-standing customer",
    createdBy: "admin",
    createdAt: new Date("2024-01-30"),
    updatedAt: new Date("2024-02-03"),
  },
  {
    id: "jc2",
    jobNumber: "JOB-2024-002",
    groupId: "g2",
    productId: "p2",
    merchantId: "m1",
    groupName: "Family Heritage Stone",
    productName: "Heritage Family Stone",
    productImage: getStoneImagesForCategory("premium", 1)[0] || "",
    status: "pending",
    priority: "medium",
    assignedTeam: ["t3", "t4"],
    teamLeadId: "t3",
    estimatedStartDate: new Date("2024-02-10"),
    estimatedCompletionDate: new Date("2024-02-25"),
    phases: [
      {
        id: "jcp3",
        phaseId: "ph1",
        phaseName: "Design Approval",
        description: "Customer approves final design",
        estimatedDuration: 2,
        status: "pending",
        assignedWorkers: ["t3"],
        order: 1,
      },
    ],
    materialRequirements: [
      {
        id: "jcm2",
        materialId: "rm2",
        materialName: "Premium Granite - Grey",
        requiredQuantity: 3.0,
        usedQuantity: 0,
        unit: "m²",
        costPerUnit: convertToZAR(520),
        totalCost: convertToZAR(1560),
        status: "pending",
      },
    ],
    createdBy: "admin",
    createdAt: new Date("2024-02-01"),
    updatedAt: new Date("2024-02-01"),
  },
];

export default function JobCards() {
  const [jobCards, setJobCards] = useState<JobCard[]>(mockJobCards);
  const [filteredJobCards, setFilteredJobCards] =
    useState<JobCard[]>(mockJobCards);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");

  // Filter job cards based on search and filters
  useEffect(() => {
    let filtered = jobCards;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (jobCard) =>
          jobCard.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
          jobCard.groupName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          jobCard.productName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((jobCard) => jobCard.status === statusFilter);
    }

    // Priority filter
    if (priorityFilter !== "all") {
      filtered = filtered.filter(
        (jobCard) => jobCard.priority === priorityFilter
      );
    }

    setFilteredJobCards(filtered);
  }, [jobCards, searchTerm, statusFilter, priorityFilter]);

  const getStatusColor = (status: JobCard["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "on_hold":
        return "bg-orange-100 text-orange-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: JobCard["status"]) => {
    switch (status) {
      case "pending":
        return <Clock className="h-3 w-3" />;
      case "in_progress":
        return <Package className="h-3 w-3" />;
      case "completed":
        return <CheckCircle className="h-3 w-3" />;
      case "on_hold":
        return <Pause className="h-3 w-3" />;
      case "cancelled":
        return <X className="h-3 w-3" />;
      default:
        return <FileText className="h-3 w-3" />;
    }
  };

  const getPriorityColor = (priority: JobCard["priority"]) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "medium":
        return "bg-blue-100 text-blue-800";
      case "low":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const calculateProgress = (phases: JobCard["phases"]) => {
    if (phases.length === 0) return 0;
    const completedPhases = phases.filter(
      (phase) => phase.status === "completed"
    ).length;
    return Math.round((completedPhases / phases.length) * 100);
  };

  // Summary statistics
  const totalJobs = jobCards.length;
  const pendingJobs = jobCards.filter((job) => job.status === "pending").length;
  const inProgressJobs = jobCards.filter(
    (job) => job.status === "in_progress"
  ).length;
  const completedJobs = jobCards.filter(
    (job) => job.status === "completed"
  ).length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Job Cards"
        description="Manage manufacturing job cards and track progress"
        actions={
          <Link href="/merchant/manufacturing/job-cards/generate">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Generate Job Card
            </Button>
          </Link>
        }
      />

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalJobs}</div>
            <p className="text-xs text-muted-foreground">Active job cards</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingJobs}</div>
            <p className="text-xs text-muted-foreground">Awaiting start</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Package className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inProgressJobs}</div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedJobs}</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search job cards..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Job Cards List */}
      <div className="space-y-4">
        {filteredJobCards.map((jobCard) => (
          <Card key={jobCard.id}>
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Image
                    src={jobCard.productImage || "/images/placeholder.png"}
                    alt={jobCard.productName}
                    width={64}
                    height={64}
                    className="rounded-lg object-cover"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h3 className="font-semibold text-lg">
                        {jobCard.jobNumber}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {jobCard.groupName} - {jobCard.productName}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getPriorityColor(jobCard.priority)}>
                        {jobCard.priority}
                      </Badge>
                      <Badge className={getStatusColor(jobCard.status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(jobCard.status)}
                          <span className="capitalize">
                            {jobCard.status.replace("_", " ")}
                          </span>
                        </div>
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-muted-foreground">Progress</p>
                      <p className="font-medium">
                        {calculateProgress(jobCard.phases)}%
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Team Size</p>
                      <p className="font-medium">
                        {jobCard.assignedTeam.length} members
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">
                        Start Date
                      </p>
                      <p className="font-medium">
                        {(
                          jobCard.actualStartDate || jobCard.estimatedStartDate
                        ).toLocaleDateString("en-ZA", {
                          month: "short",
                          day: "numeric",
                        })}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Due Date</p>
                      <p className="font-medium">
                        {(
                          jobCard.actualCompletionDate ||
                          jobCard.estimatedCompletionDate
                        ).toLocaleDateString("en-ZA", {
                          month: "short",
                          day: "numeric",
                        })}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Users className="h-3 w-3" />
                        <span>{jobCard.phases.length} phases</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Package className="h-3 w-3" />
                        <span>
                          {jobCard.materialRequirements.length} materials
                        </span>
                      </div>
                      {jobCard.notes && (
                        <div className="flex items-center space-x-1">
                          <FileText className="h-3 w-3" />
                          <span>Notes</span>
                        </div>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        href={`/merchant/manufacturing/job-cards/${jobCard.id}`}
                      >
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </Link>
                      <Button size="sm">Update Progress</Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredJobCards.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No job cards found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ||
                statusFilter !== "all" ||
                priorityFilter !== "all"
                  ? "Try adjusting your search or filters"
                  : "Generate your first job card to get started"}
              </p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Generate Job Card
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
