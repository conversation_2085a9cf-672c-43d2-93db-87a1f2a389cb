"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  FileText,
  Clock,
  Users,
  CheckCircle,
  Package,
  Calendar,
  User,
  AlertTriangle,
  Edit,
  Download,
  Printer,
} from "lucide-react";
import { JobCard, TeamMember } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock job card data
const mockJobCard: JobCard = {
  id: "jc1",
  jobNumber: "JOB-2024-001",
  groupId: "g1",
  productId: "p1",
  merchantId: "m1",
  groupName: "Memorial for John Smith",
  productName: "Classic Granite Memorial",
  productImage: getStoneImagesForCategory("traditional", 1)[0] || "",
  status: "in_progress",
  priority: "high",
  assignedTeam: ["t1", "t2"],
  teamLeadId: "t1",
  estimatedStartDate: new Date("2024-02-01"),
  actualStartDate: new Date("2024-02-01"),
  estimatedCompletionDate: new Date("2024-02-15"),
  phases: [
    {
      id: "jcp1",
      phaseId: "ph1",
      phaseName: "Design Approval",
      description: "Customer approves final design and specifications",
      estimatedDuration: 2,
      actualDuration: 1,
      status: "completed",
      assignedWorkers: ["t1"],
      startDate: new Date("2024-02-01"),
      completionDate: new Date("2024-02-02"),
      notes: "Completed ahead of schedule",
      order: 1,
    },
    {
      id: "jcp2",
      phaseId: "ph2",
      phaseName: "Stone Cutting",
      description: "Cut granite to specifications using CNC machine",
      estimatedDuration: 5,
      status: "in_progress",
      assignedWorkers: ["t1", "t2"],
      startDate: new Date("2024-02-03"),
      notes: "Using premium black granite from Mpumalanga",
      order: 2,
    },
    {
      id: "jcp3",
      phaseId: "ph3",
      phaseName: "Engraving",
      description: "Engrave memorial text and designs",
      estimatedDuration: 3,
      status: "pending",
      assignedWorkers: ["t3"],
      order: 3,
    },
    {
      id: "jcp4",
      phaseId: "ph4",
      phaseName: "Final Polish",
      description: "Final polish and quality check",
      estimatedDuration: 2,
      status: "pending",
      assignedWorkers: ["t1", "t2"],
      order: 4,
    },
  ],
  materialRequirements: [
    {
      id: "jcm1",
      materialId: "rm1",
      materialName: "Premium Black Granite",
      requiredQuantity: 2.5,
      usedQuantity: 1.2,
      unit: "m²",
      costPerUnit: convertToZAR(450),
      totalCost: convertToZAR(1125),
      status: "allocated",
      allocatedBy: "admin",
      allocatedAt: new Date("2024-02-01"),
      usedBy: "t1",
      usedAt: new Date("2024-02-03"),
    },
    {
      id: "jcm2",
      materialId: "rm2",
      materialName: "Engraving Tools",
      requiredQuantity: 1,
      usedQuantity: 0,
      unit: "set",
      costPerUnit: convertToZAR(150),
      totalCost: convertToZAR(150),
      status: "pending",
    },
    {
      id: "jcm3",
      materialId: "rm3",
      materialName: "Polish Compound",
      requiredQuantity: 0.5,
      usedQuantity: 0,
      unit: "kg",
      costPerUnit: convertToZAR(80),
      totalCost: convertToZAR(40),
      status: "pending",
    },
  ],
  notes:
    "High priority job for long-standing customer. Customer requested premium finish.",
  createdBy: "admin",
  createdAt: new Date("2024-01-30"),
  updatedAt: new Date("2024-02-03"),
};

// Mock team members
const mockTeamMembers: TeamMember[] = [
  {
    id: "t1",
    merchantId: "m1",
    name: "Thabo Mthembu",
    email: "<EMAIL>",
    phone: "+27 82 123 4567",
    photoUrl: "/inventory/stone-15.jpeg",
    role: "factory_manager",
    specialties: ["Stone Cutting", "CNC Operation", "Quality Control"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-06-01"),
  },
  {
    id: "t2",
    merchantId: "m1",
    name: "Sarah van der Merwe",
    email: "<EMAIL>",
    phone: "+27 83 234 5678",
    photoUrl: "/inventory/stone-16.jpeg",
    role: "factory_worker",
    specialties: ["Stone Cutting", "Polishing", "General Labor"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-08-15"),
  },
  {
    id: "t3",
    merchantId: "m1",
    name: "Johan Pretorius",
    email: "<EMAIL>",
    phone: "+27 84 345 6789",
    photoUrl: "/inventory/stone-17.jpeg",
    role: "factory_worker",
    specialties: ["Engraving", "Detail Work", "Artistic Design"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-09-01"),
  },
];

export default function JobCardDetails() {
  const params = useParams();
  const jobCardId = params.id as string;
  const [jobCard] = useState<JobCard>(mockJobCard);
  const [teamMembers] = useState<TeamMember[]>(mockTeamMembers);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "on_hold":
        return "bg-orange-100 text-orange-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: JobCard["priority"]) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "medium":
        return "bg-blue-100 text-blue-800";
      case "low":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const calculateProgress = () => {
    const completedPhases = jobCard.phases.filter(
      (phase) => phase.status === "completed"
    ).length;
    return Math.round((completedPhases / jobCard.phases.length) * 100);
  };

  const getTeamMember = (id: string) => {
    return teamMembers.find((member) => member.id === id);
  };

  const totalMaterialCost = jobCard.materialRequirements.reduce(
    (sum, material) => sum + material.totalCost,
    0
  );

  const usedMaterialCost = jobCard.materialRequirements.reduce(
    (sum, material) =>
      sum +
      (material.usedQuantity / material.requiredQuantity) * material.totalCost,
    0
  );

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title={jobCard.jobNumber}
        description={`${jobCard.groupName} - ${jobCard.productName}`}
        backHref="/merchant/manufacturing/job-cards"
        actions={
          <div className="flex space-x-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button variant="outline">
              <Printer className="mr-2 h-4 w-4" />
              Print
            </Button>
            <Button>
              <Edit className="mr-2 h-4 w-4" />
              Update Progress
            </Button>
          </div>
        }
      />

      {/* Status and Priority Badges */}
      <div className="flex items-center space-x-2 -mt-4">
        <Badge className={getStatusColor(jobCard.status)}>
          {jobCard.status.replace("_", " ")}
        </Badge>
        <Badge className={getPriorityColor(jobCard.priority)}>
          {jobCard.priority} priority
        </Badge>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Progress</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{calculateProgress()}%</div>
            <Progress value={calculateProgress()} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Size</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {jobCard.assignedTeam.length}
            </div>
            <p className="text-xs text-muted-foreground">team members</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Material Cost</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatZAR(totalMaterialCost)}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatZAR(usedMaterialCost)} used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {jobCard.phases.reduce(
                (sum, phase) => sum + phase.estimatedDuration,
                0
              )}
            </div>
            <p className="text-xs text-muted-foreground">estimated days</p>
          </CardContent>
        </Card>
      </div>

      {/* Product Image and Basic Info */}
      <div className="grid gap-6 lg:grid-cols-3">
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Product</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="aspect-square relative rounded-lg overflow-hidden">
              <Image
                src={jobCard.productImage || "/placeholder-product.jpg"}
                alt={jobCard.productName}
                fill
                className="object-cover"
              />
            </div>
            <div>
              <h3 className="font-semibold">{jobCard.productName}</h3>
              <p className="text-sm text-muted-foreground">
                {jobCard.groupName}
              </p>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Start Date:</span>
                <span>
                  {(
                    jobCard.actualStartDate || jobCard.estimatedStartDate
                  ).toLocaleDateString("en-ZA")}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Due Date:</span>
                <span>
                  {(
                    jobCard.actualCompletionDate ||
                    jobCard.estimatedCompletionDate
                  ).toLocaleDateString("en-ZA")}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Created:</span>
                <span>{jobCard.createdAt.toLocaleDateString("en-ZA")}</span>
              </div>
            </div>
            {jobCard.notes && (
              <div>
                <h4 className="font-medium mb-2">Notes</h4>
                <p className="text-sm text-muted-foreground">{jobCard.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="lg:col-span-2">
          <Tabs defaultValue="phases" className="space-y-4">
            <TabsList>
              <TabsTrigger value="phases">Phases</TabsTrigger>
              <TabsTrigger value="materials">Materials</TabsTrigger>
              <TabsTrigger value="team">Team</TabsTrigger>
            </TabsList>

            <TabsContent value="phases" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Manufacturing Phases</CardTitle>
                  <CardDescription>
                    Track progress through each manufacturing phase
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {jobCard.phases.map((phase, index) => (
                    <div key={phase.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-semibold">{phase.phaseName}</h4>
                          <p className="text-sm text-muted-foreground">
                            {phase.description}
                          </p>
                        </div>
                        <Badge className={getStatusColor(phase.status)}>
                          {phase.status.replace("_", " ")}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                        <div>
                          <p className="text-xs text-muted-foreground">
                            Duration
                          </p>
                          <p className="font-medium">
                            {phase.actualDuration || phase.estimatedDuration}{" "}
                            days
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">
                            Workers
                          </p>
                          <p className="font-medium">
                            {phase.assignedWorkers.length}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">
                            Start Date
                          </p>
                          <p className="font-medium">
                            {phase.startDate
                              ? phase.startDate.toLocaleDateString("en-ZA", {
                                  month: "short",
                                  day: "numeric",
                                })
                              : "Not started"}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">
                            Completion
                          </p>
                          <p className="font-medium">
                            {phase.completionDate
                              ? phase.completionDate.toLocaleDateString(
                                  "en-ZA",
                                  {
                                    month: "short",
                                    day: "numeric",
                                  }
                                )
                              : "Pending"}
                          </p>
                        </div>
                      </div>

                      {phase.assignedWorkers.length > 0 && (
                        <div className="mb-3">
                          <p className="text-xs text-muted-foreground mb-2">
                            Assigned Workers
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {phase.assignedWorkers.map((workerId) => {
                              const worker = getTeamMember(workerId);
                              return worker ? (
                                <Badge key={workerId} variant="outline">
                                  {worker.name}
                                </Badge>
                              ) : null;
                            })}
                          </div>
                        </div>
                      )}

                      {phase.notes && (
                        <div>
                          <p className="text-xs text-muted-foreground mb-1">
                            Notes
                          </p>
                          <p className="text-sm">{phase.notes}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="materials" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Material Requirements</CardTitle>
                  <CardDescription>
                    Track material allocation and usage for this job
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {jobCard.materialRequirements.map((material) => (
                      <div key={material.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-semibold">
                              {material.materialName}
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              {material.requiredQuantity} {material.unit}{" "}
                              required
                            </p>
                          </div>
                          <Badge className={getStatusColor(material.status)}>
                            {material.status}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                          <div>
                            <p className="text-xs text-muted-foreground">
                              Required
                            </p>
                            <p className="font-medium">
                              {material.requiredQuantity} {material.unit}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">
                              Used
                            </p>
                            <p className="font-medium">
                              {material.usedQuantity} {material.unit}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">
                              Cost per Unit
                            </p>
                            <p className="font-medium">
                              {formatZAR(material.costPerUnit)}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">
                              Total Cost
                            </p>
                            <p className="font-medium">
                              {formatZAR(material.totalCost)}
                            </p>
                          </div>
                        </div>

                        {material.status !== "pending" && (
                          <div className="text-sm text-muted-foreground">
                            {material.allocatedBy && (
                              <p>
                                Allocated by {material.allocatedBy} on{" "}
                                {material.allocatedAt?.toLocaleDateString(
                                  "en-ZA"
                                )}
                              </p>
                            )}
                            {material.usedBy && (
                              <p>
                                Used by {material.usedBy} on{" "}
                                {material.usedAt?.toLocaleDateString("en-ZA")}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="team" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Assigned Team</CardTitle>
                  <CardDescription>
                    Team members working on this job card
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {jobCard.assignedTeam.map((memberId) => {
                      const member = getTeamMember(memberId);
                      if (!member) return null;

                      const isTeamLead = memberId === jobCard.teamLeadId;
                      return (
                        <div
                          key={memberId}
                          className="flex items-center space-x-4 p-4 border rounded-lg"
                        >
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                              {member.photoUrl ? (
                                <Image
                                  src={member.photoUrl}
                                  alt={member.name}
                                  width={48}
                                  height={48}
                                  className="rounded-full object-cover"
                                />
                              ) : (
                                <User className="h-6 w-6 text-gray-500" />
                              )}
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-semibold">{member.name}</h4>
                              {isTeamLead && (
                                <Badge variant="outline">Team Lead</Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground capitalize">
                              {member.role.replace("_", " ")}
                            </p>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {member.specialties
                                .slice(0, 3)
                                .map((specialty, index) => (
                                  <Badge
                                    key={index}
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {specialty}
                                  </Badge>
                                ))}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-muted-foreground">
                              {member.phone}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {member.email}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
