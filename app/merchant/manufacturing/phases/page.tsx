"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  ChevronLeft,
  Plus,
  Save,
  Edit,
  Trash2,
  GripVertical,
  Clock,
  Settings,
} from "lucide-react";
import { GlobalManufacturingPhase } from "@/types/merchant";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data - replace with actual API calls
const mockGlobalPhases: GlobalManufacturingPhase[] = [
  {
    id: "gph1",
    merchantId: "m1",
    name: "Design Approval",
    description: "Customer approves final design and specifications",
    estimatedDuration: 2,
    order: 1,
    isRequired: true,
    isGlobal: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "gph2",
    merchantId: "m1",
    name: "Material Selection",
    description: "Select and prepare materials for manufacturing",
    estimatedDuration: 3,
    order: 2,
    isRequired: true,
    isGlobal: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "gph3",
    merchantId: "m1",
    name: "Manufacturing",
    description: "Core manufacturing process",
    estimatedDuration: 14,
    order: 3,
    isRequired: true,
    isGlobal: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "gph4",
    merchantId: "m1",
    name: "Quality Control",
    description: "Quality inspection and testing",
    estimatedDuration: 2,
    order: 4,
    isRequired: true,
    isGlobal: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "gph5",
    merchantId: "m1",
    name: "Final Finishing",
    description: "Final polish and finishing touches",
    estimatedDuration: 3,
    order: 5,
    isRequired: true,
    isGlobal: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

export default function GlobalManufacturingPhases() {
  const [phases, setPhases] =
    useState<GlobalManufacturingPhase[]>(mockGlobalPhases);
  const [isAddingPhase, setIsAddingPhase] = useState(false);
  const [editingPhase, setEditingPhase] = useState<string | null>(null);

  const [newPhase, setNewPhase] = useState({
    name: "",
    description: "",
    estimatedDuration: 1,
    isRequired: true,
  });

  const totalDuration = phases.reduce(
    (sum, phase) => sum + phase.estimatedDuration,
    0
  );

  const handleAddPhase = () => {
    const phase: GlobalManufacturingPhase = {
      id: `gph${Date.now()}`,
      merchantId: "m1",
      name: newPhase.name,
      description: newPhase.description,
      estimatedDuration: newPhase.estimatedDuration,
      order: phases.length + 1,
      isRequired: newPhase.isRequired,
      isGlobal: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setPhases([...phases, phase]);
    setNewPhase({
      name: "",
      description: "",
      estimatedDuration: 1,
      isRequired: true,
    });
    setIsAddingPhase(false);
  };

  const handleDeletePhase = (phaseId: string) => {
    setPhases(phases.filter((p) => p.id !== phaseId));
  };

  const handleSavePhases = () => {
    // TODO: Implement actual phase saving
    console.log("Saving global phases:", phases);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Global Manufacturing Phases"
        description="Define standard manufacturing phases that apply to all products"
        backHref="/merchant/manufacturing"
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => setIsAddingPhase(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Phase
            </Button>
            <Button onClick={handleSavePhases}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        }
      />

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Global Phases Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{phases.length}</p>
              <p className="text-sm text-muted-foreground">Total Phases</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{totalDuration}</p>
              <p className="text-sm text-muted-foreground">Total Days</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">
                {phases.filter((p) => p.isRequired).length}
              </p>
              <p className="text-sm text-muted-foreground">Required Phases</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add New Phase */}
      {isAddingPhase && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Global Phase</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phaseName">Phase Name</Label>
                <Input
                  id="phaseName"
                  value={newPhase.name}
                  onChange={(e) =>
                    setNewPhase((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="e.g., Stone Cutting"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="duration">Duration (days)</Label>
                <Input
                  id="duration"
                  type="number"
                  min="1"
                  value={newPhase.estimatedDuration}
                  onChange={(e) =>
                    setNewPhase((prev) => ({
                      ...prev,
                      estimatedDuration: parseInt(e.target.value),
                    }))
                  }
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phaseDescription">Description</Label>
              <Textarea
                id="phaseDescription"
                value={newPhase.description}
                onChange={(e) =>
                  setNewPhase((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Describe what happens in this phase..."
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddingPhase(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddPhase}>Add Phase</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Global Phases */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Global Manufacturing Phases</h3>

        {phases.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground mb-4">
                No global manufacturing phases defined yet.
              </p>
              <Button onClick={() => setIsAddingPhase(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add First Phase
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-3">
            {phases.map((phase, index) => (
              <Card key={phase.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                        <Badge variant="outline">Phase {index + 1}</Badge>
                      </div>

                      <div className="flex-1">
                        <h4 className="font-semibold">{phase.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {phase.description}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>{phase.estimatedDuration} days</span>
                      </div>

                      {phase.isRequired && (
                        <Badge variant="secondary">Required</Badge>
                      )}

                      <Badge className="bg-blue-100 text-blue-800">
                        Global
                      </Badge>

                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingPhase(phase.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeletePhase(phase.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Phase Timeline Preview */}
      {phases.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Global Manufacturing Timeline</CardTitle>
            <CardDescription>
              Standard timeline that will be applied to all products
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {phases.map((phase, index) => (
                <div key={phase.id} className="flex items-center space-x-4">
                  <div className="flex flex-col items-center">
                    <div className="w-3 h-3 rounded-full bg-primary" />
                    {index < phases.length - 1 && (
                      <div className="w-px h-8 bg-border mt-2" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{phase.name}</h4>
                      <span className="text-sm text-muted-foreground">
                        {phase.estimatedDuration} days
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {phase.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
