"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Plus,
  Search,
  Filter,
  TrendingUp,
  Eye,
  Edit,
  MoreHorizontal,
  Package,
  DollarSign,
  Star,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { UpsellItem } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock upsell data
const mockUpsells: UpsellItem[] = [
  {
    id: "ups1",
    merchantId: "m1",
    name: "Premium Engraving Service",
    description: "Professional laser engraving with custom designs and text",
    price: convertToZAR(299.99),
    images: ["/images/engraving-service.jpg"],
    category: "service",
    specifications: {
      "Service Type": "Laser Engraving",
      "Max Characters": "200",
      "Design Options": "Text, Symbols, Images",
      "Turnaround": "2-3 days",
    },
    isActive: true,
    stockStatus: "in_stock",
    applicableProducts: ["inv1", "inv2", "inv3"],
    upsellType: "service",
    priority: 1,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "ups2",
    merchantId: "m1",
    name: "Protective Stone Sealant",
    description: "Weather-resistant sealant to protect memorial stones from elements",
    price: convertToZAR(149.99),
    images: ["/images/stone-sealant.jpg"],
    category: "accessory",
    specifications: {
      Coverage: "Up to 2m²",
      "Protection Duration": "5 years",
      "Weather Resistance": "UV, Rain, Frost",
      Application: "Professional",
    },
    isActive: true,
    stockStatus: "in_stock",
    applicableProducts: ["inv1", "inv2", "inv3", "inv4"],
    upsellType: "accessory",
    priority: 2,
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "ups3",
    merchantId: "m1",
    name: "Extended Warranty (5 Years)",
    description: "Comprehensive warranty covering manufacturing defects and weather damage",
    price: convertToZAR(499.99),
    images: ["/images/warranty-certificate.jpg"],
    category: "warranty",
    specifications: {
      Duration: "5 Years",
      Coverage: "Manufacturing defects, Weather damage",
      "Repair Service": "Free on-site repairs",
      "Replacement": "Full replacement if unrepairable",
    },
    isActive: true,
    stockStatus: "in_stock",
    applicableProducts: ["inv1", "inv2", "inv3", "inv4"],
    upsellType: "warranty",
    priority: 3,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-25"),
  },
  {
    id: "ups4",
    merchantId: "m1",
    name: "Premium Installation Service",
    description: "Professional installation with site preparation and cleanup",
    price: convertToZAR(799.99),
    images: ["/images/installation-service.jpg"],
    category: "service",
    specifications: {
      "Service Includes": "Site prep, Installation, Cleanup",
      "Team Size": "2-3 professionals",
      Duration: "Half day",
      "Follow-up": "30-day check",
    },
    isActive: true,
    stockStatus: "in_stock",
    applicableProducts: ["inv3", "inv4"],
    upsellType: "service",
    priority: 1,
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-28"),
  },
];

const upsellCategories = [
  { value: "all", label: "All Categories" },
  { value: "service", label: "Services" },
  { value: "accessory", label: "Accessories" },
  { value: "warranty", label: "Warranties" },
  { value: "upgrade", label: "Upgrades" },
];

const upsellTypes = [
  { value: "all", label: "All Types" },
  { value: "accessory", label: "Accessory" },
  { value: "upgrade", label: "Upgrade" },
  { value: "service", label: "Service" },
  { value: "warranty", label: "Warranty" },
];

export default function UpsellsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  const filteredUpsells = mockUpsells.filter((upsell) => {
    const matchesSearch = upsell.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" || upsell.category === selectedCategory;
    const matchesType =
      selectedType === "all" || upsell.upsellType === selectedType;
    const matchesStatus =
      selectedStatus === "all" ||
      (selectedStatus === "active" && upsell.isActive) ||
      (selectedStatus === "inactive" && !upsell.isActive);

    return matchesSearch && matchesCategory && matchesType && matchesStatus;
  });

  const activeUpsells = filteredUpsells.filter((upsell) => upsell.isActive).length;
  const totalRevenue = filteredUpsells.reduce((sum, upsell) => sum + upsell.price, 0);
  const averagePrice = totalRevenue / filteredUpsells.length || 0;

  const getUpsellTypeColor = (type: string) => {
    switch (type) {
      case "accessory":
        return "bg-blue-100 text-blue-800";
      case "upgrade":
        return "bg-green-100 text-green-800";
      case "service":
        return "bg-purple-100 text-purple-800";
      case "warranty":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case "in_stock":
        return "bg-green-100 text-green-800";
      case "made_to_order":
        return "bg-yellow-100 text-yellow-800";
      case "out_of_stock":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Upsells"
        description="Manage additional products and services to increase order value"
        actions={
          <div className="flex gap-2">
            <Link href="/merchant/upsells/categories">
              <Button variant="outline">
                <Package className="mr-2 h-4 w-4" />
                Categories
              </Button>
            </Link>
            <Link href="/merchant/upsells/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Upsell
              </Button>
            </Link>
          </div>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Upsells</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredUpsells.length}</div>
            <p className="text-xs text-muted-foreground">
              {activeUpsells} active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatZAR(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              Combined upsell value
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Price</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatZAR(averagePrice)}</div>
            <p className="text-xs text-muted-foreground">
              Per upsell item
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              Service, Accessory, Warranty, Upgrade
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search upsells..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {upsellCategories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              {upsellTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Upsells Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredUpsells.map((upsell) => (
          <Card key={upsell.id} className="overflow-hidden">
            <div className="aspect-video relative bg-muted">
              {upsell.images[0] && (
                <img
                  src={upsell.images[0]}
                  alt={upsell.name}
                  className="object-cover w-full h-full"
                />
              )}
              <div className="absolute top-2 right-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="secondary" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/merchant/upsells/${upsell.id}`}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/merchant/upsells/${upsell.id}/edit`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{upsell.name}</CardTitle>
                  <CardDescription className="line-clamp-2">
                    {upsell.description}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={getUpsellTypeColor(upsell.upsellType)}>
                  {upsell.upsellType}
                </Badge>
                <Badge className={getStockStatusColor(upsell.stockStatus)}>
                  {upsell.stockStatus.replace("_", " ")}
                </Badge>
                {!upsell.isActive && (
                  <Badge variant="secondary">Inactive</Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Price</span>
                  <span className="font-semibold">{formatZAR(upsell.price)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Priority</span>
                  <span className="text-sm">{upsell.priority}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Applicable Products</span>
                  <span className="text-sm">{upsell.applicableProducts.length}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredUpsells.length === 0 && (
        <div className="text-center py-12">
          <TrendingUp className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">No upsells found</h3>
          <p className="text-muted-foreground">
            {searchTerm || selectedCategory !== "all" || selectedType !== "all"
              ? "Try adjusting your filters"
              : "Get started by creating your first upsell item"}
          </p>
          {!searchTerm && selectedCategory === "all" && selectedType === "all" && (
            <Link href="/merchant/upsells/new">
              <Button className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                Add Upsell
              </Button>
            </Link>
          )}
        </div>
      )}
    </div>
  );
}
