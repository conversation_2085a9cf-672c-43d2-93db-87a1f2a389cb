"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Plus,
  Edit,
  Trash2,
  Package,
  TrendingUp,
  Save,
  X,
} from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { UpsellCategory } from "@/types/merchant";

// Mock upsell categories data
const mockCategories: UpsellCategory[] = [
  {
    id: "cat1",
    merchantId: "m1",
    name: "Services",
    description: "Additional services like engraving, installation, and maintenance",
    slug: "services",
    color: "#8B5CF6",
    icon: "🔧",
    isActive: true,
    itemCount: 3,
    displayOrder: 1,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "cat2",
    merchantId: "m1",
    name: "Accessories",
    description: "Physical add-ons like sealants, plaques, and decorative elements",
    slug: "accessories",
    color: "#10B981",
    icon: "🎨",
    isActive: true,
    itemCount: 2,
    displayOrder: 2,
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "cat3",
    merchantId: "m1",
    name: "Warranties",
    description: "Extended warranty and protection plans",
    slug: "warranties",
    color: "#F59E0B",
    icon: "🛡️",
    isActive: true,
    itemCount: 1,
    displayOrder: 3,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-25"),
  },
  {
    id: "cat4",
    merchantId: "m1",
    name: "Upgrades",
    description: "Premium material and design upgrades",
    slug: "upgrades",
    color: "#EF4444",
    icon: "⭐",
    isActive: false,
    itemCount: 0,
    displayOrder: 4,
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-28"),
  },
];

export default function UpsellCategoriesPage() {
  const [categories, setCategories] = useState<UpsellCategory[]>(mockCategories);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState({
    name: "",
    description: "",
    color: "#2563EB",
    icon: "",
    isActive: true,
  });

  const handleAddCategory = () => {
    const category: UpsellCategory = {
      id: `cat${Date.now()}`,
      merchantId: "m1",
      name: newCategory.name,
      description: newCategory.description,
      slug: newCategory.name.toLowerCase().replace(/\s+/g, "-"),
      color: newCategory.color,
      icon: newCategory.icon,
      isActive: newCategory.isActive,
      itemCount: 0,
      displayOrder: categories.length + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setCategories([...categories, category]);
    setNewCategory({
      name: "",
      description: "",
      color: "#2563EB",
      icon: "",
      isActive: true,
    });
    setIsAddingCategory(false);
  };

  const toggleCategoryStatus = (categoryId: string) => {
    setCategories(
      categories.map((cat) =>
        cat.id === categoryId ? { ...cat, isActive: !cat.isActive } : cat
      )
    );
  };

  const deleteCategory = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    if (category && category.itemCount > 0) {
      alert("Cannot delete category with items. Please move or delete items first.");
      return;
    }
    
    if (window.confirm("Are you sure you want to delete this category? This action cannot be undone.")) {
      setCategories(categories.filter((cat) => cat.id !== categoryId));
    }
  };

  const startEditing = (categoryId: string) => {
    const category = categories.find((cat) => cat.id === categoryId);
    if (category) {
      setEditingCategory(categoryId);
      setNewCategory({
        name: category.name,
        description: category.description,
        color: category.color,
        icon: category.icon || "",
        isActive: category.isActive,
      });
    }
  };

  const saveEdit = () => {
    if (editingCategory) {
      setCategories(
        categories.map((cat) =>
          cat.id === editingCategory
            ? {
                ...cat,
                name: newCategory.name,
                description: newCategory.description,
                color: newCategory.color,
                icon: newCategory.icon,
                isActive: newCategory.isActive,
                slug: newCategory.name.toLowerCase().replace(/\s+/g, "-"),
                updatedAt: new Date(),
              }
            : cat
        )
      );
      setEditingCategory(null);
      setNewCategory({
        name: "",
        description: "",
        color: "#2563EB",
        icon: "",
        isActive: true,
      });
    }
  };

  const cancelEdit = () => {
    setEditingCategory(null);
    setIsAddingCategory(false);
    setNewCategory({
      name: "",
      description: "",
      color: "#2563EB",
      icon: "",
      isActive: true,
    });
  };

  const activeCategories = categories.filter(cat => cat.isActive).length;
  const totalItems = categories.reduce((sum, cat) => sum + cat.itemCount, 0);

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Upsell Categories"
        description="Organize your upsell items into categories for better management"
        showBackButton
        actions={
          <Button onClick={() => setIsAddingCategory(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Category
          </Button>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
            <p className="text-xs text-muted-foreground">
              {activeCategories} active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalItems}</div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {categories.length > 0 ? Math.round(totalItems / categories.length) : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Per category
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Categories Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {categories.map((category) => (
          <Card key={category.id} className="overflow-hidden">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg"
                    style={{ backgroundColor: category.color }}
                  >
                    {category.icon || "📦"}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{category.name}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {category.description}
                    </CardDescription>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={category.isActive ? "default" : "secondary"}>
                  {category.isActive ? "Active" : "Inactive"}
                </Badge>
                <Badge variant="outline">
                  {category.itemCount} items
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={category.isActive}
                    onCheckedChange={() => toggleCategoryStatus(category.id)}
                  />
                  <Label className="text-sm">Active</Label>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => startEditing(category.id)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteCategory(category.id)}
                    disabled={category.itemCount > 0}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add/Edit Category Dialog */}
      <Dialog open={isAddingCategory || editingCategory !== null} onOpenChange={(open) => {
        if (!open) cancelEdit();
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? "Edit Category" : "Add New Category"}
            </DialogTitle>
            <DialogDescription>
              {editingCategory 
                ? "Update the category details below."
                : "Create a new category to organize your upsell items."
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="categoryName">Category Name</Label>
              <Input
                id="categoryName"
                placeholder="e.g., Services, Accessories"
                value={newCategory.name}
                onChange={(e) =>
                  setNewCategory({ ...newCategory, name: e.target.value })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="categoryDescription">Description</Label>
              <Textarea
                id="categoryDescription"
                placeholder="Describe what this category contains..."
                value={newCategory.description}
                onChange={(e) =>
                  setNewCategory({ ...newCategory, description: e.target.value })
                }
                rows={3}
              />
            </div>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="categoryColor">Color</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="categoryColor"
                    type="color"
                    value={newCategory.color}
                    onChange={(e) =>
                      setNewCategory({ ...newCategory, color: e.target.value })
                    }
                    className="w-16 h-10"
                  />
                  <Input
                    value={newCategory.color}
                    onChange={(e) =>
                      setNewCategory({ ...newCategory, color: e.target.value })
                    }
                    placeholder="#2563EB"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoryIcon">Icon (Emoji)</Label>
                <Input
                  id="categoryIcon"
                  placeholder="🔧"
                  value={newCategory.icon}
                  onChange={(e) =>
                    setNewCategory({ ...newCategory, icon: e.target.value })
                  }
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="categoryActive"
                checked={newCategory.isActive}
                onCheckedChange={(checked) =>
                  setNewCategory({ ...newCategory, isActive: checked })
                }
              />
              <Label htmlFor="categoryActive">Active</Label>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={cancelEdit}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={editingCategory ? saveEdit : handleAddCategory}
              disabled={!newCategory.name || !newCategory.description}
            >
              <Save className="mr-2 h-4 w-4" />
              {editingCategory ? "Update" : "Create"} Category
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {categories.length === 0 && (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">No categories found</h3>
          <p className="text-muted-foreground">
            Get started by creating your first upsell category
          </p>
          <Button className="mt-4" onClick={() => setIsAddingCategory(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Category
          </Button>
        </div>
      )}
    </div>
  );
}
