"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Users,
  Calendar,
  DollarSign,
  Eye,
  AlertCircle,
} from "lucide-react";
import { GroupPurchase } from "@/types/merchant";
import { mockGroups } from "@/data/merchant";
import { formatZAR } from "@/data/south-african-context";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Using South African context data with product images
export default function MerchantGroups() {
  const [groups] = useState(mockGroups);
  const [filteredGroups, setFilteredGroups] =
    useState<GroupPurchase[]>(mockGroups);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [paymentFilter, setPaymentFilter] = useState<string>("all");

  useEffect(() => {
    let filtered = groups;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter((group) =>
        group.groupName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((group) => group.status === statusFilter);
    }

    // Payment progress filter
    if (paymentFilter !== "all") {
      switch (paymentFilter) {
        case "below_50":
          filtered = filtered.filter((group) => group.paymentProgress < 50);
          break;
        case "50_to_80":
          filtered = filtered.filter(
            (group) => group.paymentProgress >= 50 && group.paymentProgress < 80
          );
          break;
        case "above_80":
          filtered = filtered.filter(
            (group) =>
              group.paymentProgress >= 80 && group.paymentProgress < 100
          );
          break;
        case "completed":
          filtered = filtered.filter((group) => group.paymentProgress === 100);
          break;
      }
    }

    setFilteredGroups(filtered);
  }, [groups, searchTerm, statusFilter, paymentFilter]);

  const getStatusColor = (status: GroupPurchase["status"]) => {
    switch (status) {
      case "discussing":
        return "bg-blue-100 text-blue-800";
      case "collecting":
        return "bg-yellow-100 text-yellow-800";
      case "manufacturing":
        return "bg-purple-100 text-purple-800";
      case "installing":
        return "bg-orange-100 text-orange-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPaymentProgressColor = (progress: number) => {
    if (progress >= 80) return "text-green-600";
    if (progress >= 50) return "text-yellow-600";
    return "text-red-600";
  };

  const readyForManufacturing = filteredGroups.filter(
    (g) => g.paymentProgress >= 80 && g.status === "collecting"
  ).length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Groups"
        description="Manage all groups purchasing your products"
      />

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredGroups.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Ready for Manufacturing
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{readyForManufacturing}</div>
            <p className="text-xs text-muted-foreground">
              80%+ payment collected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatZAR(
                filteredGroups.reduce(
                  (sum, group) => sum + group.currentAmount,
                  0
                )
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Average Group Size
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(
                filteredGroups.reduce(
                  (sum, group) => sum + group.totalMembers,
                  0
                ) / filteredGroups.length || 0
              ).toFixed(1)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search groups..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="discussing">Discussing</SelectItem>
                <SelectItem value="collecting">Collecting</SelectItem>
                <SelectItem value="manufacturing">Manufacturing</SelectItem>
                <SelectItem value="installing">Installing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={paymentFilter} onValueChange={setPaymentFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Payment Progress" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Progress</SelectItem>
                <SelectItem value="below_50">Below 50%</SelectItem>
                <SelectItem value="50_to_80">50% - 80%</SelectItem>
                <SelectItem value="above_80">Above 80%</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Groups List */}
      <div className="space-y-4">
        {filteredGroups.map((group) => (
          <Card key={group.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                {/* Product Image */}
                <div className="relative w-20 h-20 flex-shrink-0 overflow-hidden rounded-lg bg-muted">
                  <Image
                    src={group.productImage || "/placeholder-product.jpg"}
                    alt={group.productName || "Product image"}
                    fill
                    className="object-cover"
                  />
                </div>

                {/* Group Details */}
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-semibold">{group.groupName}</h3>
                    <Badge className={getStatusColor(group.status)}>
                      {group.status}
                    </Badge>
                    {group.paymentProgress >= 80 &&
                      group.status === "collecting" && (
                        <Badge
                          variant="outline"
                          className="text-orange-600 border-orange-600"
                        >
                          Ready for Manufacturing
                        </Badge>
                      )}
                  </div>

                  <p className="text-sm text-muted-foreground">
                    {group.productName}
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {group.totalMembers} members
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Created {group.createdAt.toLocaleDateString("en-ZA")}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {formatZAR(group.currentAmount)} /{" "}
                        {formatZAR(group.targetAmount)}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Payment Progress</span>
                      <span
                        className={getPaymentProgressColor(
                          group.paymentProgress
                        )}
                      >
                        {group.paymentProgress}%
                      </span>
                    </div>
                    <Progress value={group.paymentProgress} className="h-2" />
                  </div>
                </div>

                {/* Action Button */}
                <div className="flex-shrink-0">
                  <Link href={`/merchant/groups/${group.id}`}>
                    <Button variant="outline">
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredGroups.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              No groups found matching your filters.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
