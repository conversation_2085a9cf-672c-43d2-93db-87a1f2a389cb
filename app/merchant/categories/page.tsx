"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  GripVertical,
  Tags,
  Package,
  Palette,
} from "lucide-react";
import { ProductCategory } from "@/types/merchant";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data - replace with actual API calls
const mockCategories: ProductCategory[] = [
  {
    id: "cat1",
    merchantId: "m1",
    name: "Traditional Memorials",
    description: "Classic granite memorial stones with timeless designs",
    slug: "traditional",
    color: "#8B5A3C",
    icon: "monument",
    isActive: true,
    productCount: 8,
    displayOrder: 1,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "cat2",
    merchantId: "m1",
    name: "Modern Memorials",
    description: "Contemporary memorial designs with clean lines",
    slug: "modern",
    color: "#2563EB",
    icon: "square",
    isActive: true,
    productCount: 5,
    displayOrder: 2,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-10"),
  },
  {
    id: "cat3",
    merchantId: "m1",
    name: "Custom Designs",
    description: "Personalized memorial stones with custom engraving",
    slug: "custom",
    color: "#7C3AED",
    icon: "star",
    isActive: true,
    productCount: 12,
    displayOrder: 3,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "cat4",
    merchantId: "m1",
    name: "Premium Granite",
    description: "High-end granite memorials with premium finishes",
    slug: "premium",
    color: "#DC2626",
    icon: "gem",
    isActive: true,
    productCount: 3,
    displayOrder: 4,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-05"),
  },
  {
    id: "cat5",
    merchantId: "m1",
    name: "Compact Memorials",
    description: "Smaller memorial plaques for limited spaces",
    slug: "compact",
    color: "#059669",
    icon: "minimize",
    isActive: false,
    productCount: 2,
    displayOrder: 5,
    createdAt: new Date("2023-12-15"),
    updatedAt: new Date("2024-01-01"),
  },
];

const colorOptions = [
  { value: "#8B5A3C", label: "Brown", color: "#8B5A3C" },
  { value: "#2563EB", label: "Blue", color: "#2563EB" },
  { value: "#7C3AED", label: "Purple", color: "#7C3AED" },
  { value: "#DC2626", label: "Red", color: "#DC2626" },
  { value: "#059669", label: "Green", color: "#059669" },
  { value: "#EA580C", label: "Orange", color: "#EA580C" },
  { value: "#0891B2", label: "Cyan", color: "#0891B2" },
  { value: "#BE185D", label: "Pink", color: "#BE185D" },
];

export default function CategoriesManagement() {
  const [categories, setCategories] =
    useState<ProductCategory[]>(mockCategories);
  const [filteredCategories, setFilteredCategories] =
    useState<ProductCategory[]>(mockCategories);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState<string | null>(null);

  const [newCategory, setNewCategory] = useState({
    name: "",
    description: "",
    color: "#2563EB",
    isActive: true,
  });

  useEffect(() => {
    let filtered = categories;

    if (searchTerm) {
      filtered = filtered.filter(
        (category) =>
          category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          category.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredCategories(filtered);
  }, [categories, searchTerm]);

  const handleAddCategory = () => {
    const category: ProductCategory = {
      id: `cat${Date.now()}`,
      merchantId: "m1",
      name: newCategory.name,
      description: newCategory.description,
      slug: newCategory.name.toLowerCase().replace(/\s+/g, "-"),
      color: newCategory.color,
      isActive: newCategory.isActive,
      productCount: 0,
      displayOrder: categories.length + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setCategories([...categories, category]);
    setNewCategory({
      name: "",
      description: "",
      color: "#2563EB",
      isActive: true,
    });
    setIsAddingCategory(false);
  };

  const toggleCategoryStatus = (categoryId: string) => {
    setCategories(
      categories.map((cat) =>
        cat.id === categoryId ? { ...cat, isActive: !cat.isActive } : cat
      )
    );
  };

  const deleteCategory = (categoryId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this category? This action cannot be undone."
      )
    ) {
      setCategories(categories.filter((cat) => cat.id !== categoryId));
    }
  };

  const startEditing = (categoryId: string) => {
    const category = categories.find((cat) => cat.id === categoryId);
    if (category) {
      setEditingCategory(categoryId);
      setNewCategory({
        name: category.name,
        description: category.description,
        color: category.color,
        isActive: category.isActive,
      });
    }
  };

  const saveEdit = () => {
    if (editingCategory) {
      setCategories(
        categories.map((cat) =>
          cat.id === editingCategory
            ? {
                ...cat,
                name: newCategory.name,
                description: newCategory.description,
                color: newCategory.color,
                isActive: newCategory.isActive,
                slug: newCategory.name.toLowerCase().replace(/\s+/g, "-"),
                updatedAt: new Date(),
              }
            : cat
        )
      );
      setEditingCategory(null);
      setNewCategory({
        name: "",
        description: "",
        color: "#2563EB",
        isActive: true,
      });
    }
  };

  const cancelEdit = () => {
    setEditingCategory(null);
    setNewCategory({
      name: "",
      description: "",
      color: "#2563EB",
      isActive: true,
    });
  };

  const activeCategories = filteredCategories.filter(
    (cat) => cat.isActive
  ).length;
  const totalProducts = filteredCategories.reduce(
    (sum, cat) => sum + cat.productCount,
    0
  );

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Categories"
        description="Manage product categories and organization"
        actions={
          <Button onClick={() => setIsAddingCategory(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Category
          </Button>
        }
      />

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Categories
            </CardTitle>
            <Tags className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredCategories.length}
            </div>
            <p className="text-xs text-muted-foreground">
              {activeCategories} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Products
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Popular</CardTitle>
            <Package className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {
                categories.reduce(
                  (max, cat) =>
                    cat.productCount > max.productCount ? cat : max,
                  categories[0]
                )?.name
              }
            </div>
            <p className="text-xs text-muted-foreground">
              {
                categories.reduce(
                  (max, cat) =>
                    cat.productCount > max.productCount ? cat : max,
                  categories[0]
                )?.productCount
              }{" "}
              products
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Average Products
            </CardTitle>
            <Package className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(totalProducts / filteredCategories.length || 0)}
            </div>
            <p className="text-xs text-muted-foreground">Per category</p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Add New Category */}
      {isAddingCategory && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Category</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="categoryName">Category Name</Label>
                <Input
                  id="categoryName"
                  value={newCategory.name}
                  onChange={(e) =>
                    setNewCategory((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="e.g., Traditional Memorials"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoryColor">Color</Label>
                <Select
                  value={newCategory.color}
                  onValueChange={(value) =>
                    setNewCategory((prev) => ({ ...prev, color: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {colorOptions.map((color) => (
                      <SelectItem key={color.value} value={color.value}>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: color.color }}
                          />
                          <span>{color.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="categoryDescription">Description</Label>
              <Textarea
                id="categoryDescription"
                value={newCategory.description}
                onChange={(e) =>
                  setNewCategory((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Describe this category..."
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsAddingCategory(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleAddCategory} disabled={!newCategory.name}>
                Add Category
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Categories List */}
      <div className="space-y-4">
        {filteredCategories.map((category) => (
          <Card key={category.id}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-semibold">{category.name}</h3>
                      <Badge
                        variant={category.isActive ? "default" : "secondary"}
                      >
                        {category.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Badge variant="outline">
                        {category.productCount} products
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {category.description}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-2">
                      <span>Slug: {category.slug}</span>
                      <span>
                        Created: {category.createdAt.toLocaleDateString()}
                      </span>
                      <span>Order: {category.displayOrder}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleCategoryStatus(category.id)}
                  >
                    {category.isActive ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                  <Link href={`/merchant/categories/${category.id}`}>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteCategory(category.id)}
                    disabled={category.productCount > 0}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                  <Link href={`/merchant/inventory?category=${category.slug}`}>
                    <Button variant="outline" size="sm">
                      View Products
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredCategories.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Tags className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">
              No categories found matching your search.
            </p>
            <Button onClick={() => setIsAddingCategory(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Your First Category
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
