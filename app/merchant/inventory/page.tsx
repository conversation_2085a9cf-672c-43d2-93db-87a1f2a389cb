"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Plus,
  Package,
  Edit,
  Eye,
  Settings,
  TrendingUp,
  AlertTriangle,
} from "lucide-react";
import { MerchantInventoryItem } from "@/types/merchant";
import { getMerchantCategories } from "@/data/merchant";
import { formatZAR, convertToZAR } from "@/data/south-african-context";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// South African inventory with real stone images
const mockInventory: MerchantInventoryItem[] = [
  {
    id: "inv1",
    merchantId: "m1",
    name: "Classic Granite Memorial Stone",
    description:
      "Traditional granite memorial stone with elegant design and premium finish. Crafted from locally sourced South African granite.",
    price: convertToZAR(1299.99),
    images: getStoneImagesForCategory("traditional", 3),
    category: "traditional",
    specifications: {
      Material: "Premium South African Granite",
      Dimensions: "61cm x 30cm x 10cm",
      Finish: "Polished",
      Engraving: "Laser Etched",
    },
    manufacturingTime: 21,
    manufacturingPhases: [],
    materialRequirements: [
      {
        materialId: "rm1",
        materialName: "Premium Black Granite",
        requiredQuantity: 1.5,
        unit: "m²",
        isOptional: false,
        notes: "Main stone material",
      },
    ],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "inv2",
    merchantId: "m1",
    name: "Modern Minimalist Memorial",
    description:
      "Contemporary memorial stone with clean lines and modern aesthetic. Perfect for modern cemetery sections.",
    price: convertToZAR(2199.99),
    images: getStoneImagesForCategory("modern", 2),
    category: "modern",
    specifications: {
      Material: "Black South African Granite",
      Dimensions: "76cm x 46cm x 15cm",
      Finish: "Honed",
      "Design Style": "Minimalist",
    },
    manufacturingTime: 28,
    manufacturingPhases: [],
    materialRequirements: [
      {
        materialId: "rm2",
        materialName: "Grey Granite",
        requiredQuantity: 2.0,
        unit: "m²",
        isOptional: false,
        notes: "Modern finish granite",
      },
      {
        materialId: "rm3",
        materialName: "Polish Compound",
        requiredQuantity: 0.5,
        unit: "kg",
        isOptional: true,
        notes: "For high-gloss finish",
      },
    ],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "inv3",
    merchantId: "m1",
    name: "Custom Engraved Memorial",
    description:
      "Personalized memorial stone with custom engraving and design options. Handcrafted by local artisans.",
    price: convertToZAR(3499.99),
    images: getStoneImagesForCategory("custom", 3),
    category: "custom",
    specifications: {
      Material: "Premium South African Granite",
      Dimensions: "Custom (up to 91cm x 61cm x 20cm)",
      "Engraving Type": "Hand Carved & Laser Etched",
      "Design Options": "Unlimited Custom",
    },
    manufacturingTime: 42,
    manufacturingPhases: [],
    materialRequirements: [
      {
        materialId: "rm1",
        materialName: "Premium Black Granite",
        requiredQuantity: 2.5,
        unit: "m²",
        isOptional: false,
        notes: "Base material for custom work",
      },
      {
        materialId: "rm4",
        materialName: "Engraving Tools",
        requiredQuantity: 1,
        unit: "set",
        isOptional: false,
        notes: "Specialized engraving equipment",
      },
    ],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2023-12-15"),
    updatedAt: new Date("2024-01-10"),
  },
  {
    id: "inv4",
    merchantId: "m1",
    name: "Premium Granite Monument",
    description:
      "High-end granite monument with premium finishes and detailed craftsmanship.",
    price: convertToZAR(5999.99),
    images: getStoneImagesForCategory("premium", 2),
    category: "premium",
    specifications: {
      Material: "Premium South African Granite",
      Dimensions: "122cm x 76cm x 25cm",
      Finish: "Polished & Honed Combination",
      Features: "Multi-level Design",
    },
    manufacturingTime: 35,
    manufacturingPhases: [],
    materialRequirements: [
      {
        materialId: "rm5",
        materialName: "Premium White Granite",
        requiredQuantity: 3.0,
        unit: "m²",
        isOptional: false,
        notes: "High-grade premium material",
      },
      {
        materialId: "rm3",
        materialName: "Polish Compound",
        requiredQuantity: 1.0,
        unit: "kg",
        isOptional: false,
        notes: "Premium finish polish",
      },
      {
        materialId: "rm6",
        materialName: "Gold Leaf",
        requiredQuantity: 0.1,
        unit: "kg",
        isOptional: true,
        notes: "For premium detailing",
      },
    ],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2023-11-20"),
    updatedAt: new Date("2024-01-05"),
  },
  {
    id: "inv5",
    merchantId: "m1",
    name: "Compact Memorial Plaque",
    description:
      "Elegant compact memorial plaque perfect for smaller spaces and garden memorials.",
    price: convertToZAR(449.99),
    images: getStoneImagesForCategory("compact", 1),
    category: "compact",
    specifications: {
      Material: "Gray South African Granite",
      Dimensions: "30cm x 20cm x 5cm",
      Mounting: "Ground Stake Included",
      Engraving: "Laser Etched",
    },
    manufacturingTime: 14,
    manufacturingPhases: [],
    materialRequirements: [
      {
        materialId: "rm2",
        materialName: "Grey Granite",
        requiredQuantity: 0.5,
        unit: "m²",
        isOptional: false,
        notes: "Compact size material",
      },
    ],
    isActive: false,
    stockStatus: "out_of_stock",
    createdAt: new Date("2023-11-20"),
    updatedAt: new Date("2024-01-05"),
  },
];

export default function MerchantInventory() {
  const [inventory, setInventory] =
    useState<MerchantInventoryItem[]>(mockInventory);
  const [filteredInventory, setFilteredInventory] =
    useState<MerchantInventoryItem[]>(mockInventory);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Get categories for this merchant
  const categories = getMerchantCategories("m1");

  useEffect(() => {
    let filtered = inventory;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (item) =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (categoryFilter !== "all") {
      filtered = filtered.filter((item) => item.category === categoryFilter);
    }

    // Status filter
    if (statusFilter !== "all") {
      if (statusFilter === "active") {
        filtered = filtered.filter((item) => item.isActive);
      } else if (statusFilter === "inactive") {
        filtered = filtered.filter((item) => !item.isActive);
      } else {
        filtered = filtered.filter((item) => item.stockStatus === statusFilter);
      }
    }

    setFilteredInventory(filtered);
  }, [inventory, searchTerm, categoryFilter, statusFilter]);

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case "in_stock":
        return "bg-green-100 text-green-800";
      case "made_to_order":
        return "bg-blue-100 text-blue-800";
      case "out_of_stock":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStockStatusLabel = (status: string) => {
    switch (status) {
      case "in_stock":
        return "In Stock";
      case "made_to_order":
        return "Made to Order";
      case "out_of_stock":
        return "Out of Stock";
      default:
        return status;
    }
  };

  const activeProducts = filteredInventory.filter(
    (item) => item.isActive
  ).length;
  const totalRevenue = filteredInventory.reduce(
    (sum, item) => sum + item.price,
    0
  );
  const averagePrice = totalRevenue / filteredInventory.length || 0;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Inventory"
        description="Manage your product catalog and manufacturing settings"
        actions={
          <Link href="/merchant/inventory/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </Link>
        }
      />

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Products
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredInventory.length}</div>
            <p className="text-xs text-muted-foreground">
              {activeProducts} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Price</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatZAR(Math.round(averagePrice))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(filteredInventory.map((item) => item.category)).size}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {
                filteredInventory.filter(
                  (item) => item.stockStatus === "out_of_stock"
                ).length
              }
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.slug}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="in_stock">In Stock</SelectItem>
                <SelectItem value="made_to_order">Made to Order</SelectItem>
                <SelectItem value="out_of_stock">Out of Stock</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Products Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredInventory.map((item) => (
          <Card key={item.id} className={!item.isActive ? "opacity-60" : ""}>
            <CardHeader className="p-0">
              <div className="aspect-video relative overflow-hidden rounded-t-lg">
                <img
                  src={item.images[0]}
                  alt={item.name}
                  className="object-cover w-full h-full"
                />
                <div className="absolute top-2 right-2 flex gap-2">
                  <Badge className={getStockStatusColor(item.stockStatus)}>
                    {getStockStatusLabel(item.stockStatus)}
                  </Badge>
                  {!item.isActive && (
                    <Badge variant="secondary">Inactive</Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <h3 className="font-semibold text-lg leading-tight">
                    {item.name}
                  </h3>
                </div>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {item.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">
                    {formatZAR(item.price)}
                  </span>
                  <Badge variant="outline" className="capitalize">
                    {item.category}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  Manufacturing: {item.manufacturingTime} days
                </div>
                <div className="flex gap-2 pt-2">
                  <Link
                    href={`/merchant/inventory/${item.id}`}
                    className="flex-1"
                  >
                    <Button variant="outline" size="sm" className="w-full">
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </Button>
                  </Link>
                  <Link
                    href={`/merchant/inventory/${item.id}/manufacturing`}
                    className="flex-1"
                  >
                    <Button variant="outline" size="sm" className="w-full">
                      <Settings className="mr-2 h-4 w-4" />
                      Phases
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredInventory.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">
              No products found matching your filters.
            </p>
            <Link href="/merchant/inventory/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Product
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
