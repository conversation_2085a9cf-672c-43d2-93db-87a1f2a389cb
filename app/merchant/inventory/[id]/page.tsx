"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ChevronLeft,
  Save,
  Settings,
  Package,
  Edit,
  Star,
  Trash2,
  Plus,
} from "lucide-react";
import { MerchantInventoryItem } from "@/types/merchant";
import { getMerchantCategories } from "@/data/merchant";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data - replace with actual API calls
const mockProduct: MerchantInventoryItem & { applyGlobalPhases?: boolean } = {
  id: "inv1",
  merchantId: "m1",
  name: "Classic Granite Memorial Stone",
  description:
    "Traditional granite memorial stone with elegant design and premium finish. Perfect for honoring loved ones with timeless beauty and durability.",
  price: 1299.99,
  images: [
    "/inventory/stone-1.jpeg",
    "/inventory/stone-2.jpeg",
    "/inventory/stone-3.jpeg",
  ],
  category: "traditional",
  specifications: {
    Material: "Premium Granite",
    Dimensions: '24" x 12" x 4"',
    Finish: "Polished",
    Engraving: "Laser Etched",
    Color: "Charcoal Gray",
    "Weather Resistant": "Yes",
  },
  manufacturingTime: 21,
  manufacturingPhases: [],
  materialRequirements: [
    {
      materialId: "rm1",
      materialName: "Premium Black Granite",
      requiredQuantity: 2.5,
      unit: "m²",
      isOptional: false,
      notes: "Main stone material",
    },
    {
      materialId: "rm2",
      materialName: "Engraving Tools",
      requiredQuantity: 1,
      unit: "set",
      isOptional: false,
      notes: "For custom engraving work",
    },
    {
      materialId: "rm3",
      materialName: "Polish Compound",
      requiredQuantity: 0.5,
      unit: "kg",
      isOptional: true,
      notes: "For premium finish",
    },
  ],
  isActive: true,
  stockStatus: "made_to_order",
  applyGlobalPhases: true,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-15"),
};

export default function EditProduct() {
  const params = useParams();
  const productId = params.id as string;

  const [product, setProduct] = useState<MerchantInventoryItem>(mockProduct);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(mockProduct);
  const [mainImageIndex, setMainImageIndex] = useState(0);

  // Material requirements management
  const [isAddingMaterial, setIsAddingMaterial] = useState(false);
  const [editingMaterialIndex, setEditingMaterialIndex] = useState<
    number | null
  >(null);
  const [deletingMaterialIndex, setDeletingMaterialIndex] = useState<
    number | null
  >(null);
  const [newMaterial, setNewMaterial] = useState({
    materialId: "",
    materialName: "",
    requiredQuantity: 0,
    unit: "",
    isOptional: false,
    notes: "",
  });

  // Get categories for this merchant
  const categories = getMerchantCategories("m1");

  const stockStatuses = [
    { value: "in_stock", label: "In Stock" },
    { value: "made_to_order", label: "Made to Order" },
    { value: "out_of_stock", label: "Out of Stock" },
  ];

  const handleInputChange = (
    field: string,
    value: string | number | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    // TODO: Implement actual product update
    setProduct(formData);
    setIsEditing(false);
    console.log("Saving product:", formData);
  };

  const handleCancel = () => {
    setFormData(product);
    setIsEditing(false);
  };

  const handleSetMainImage = (index: number) => {
    setMainImageIndex(index);
    // TODO: Update the product's image order to make this the main image
    console.log(`Setting image ${index} as main image`);
  };

  const handleRemoveImage = (index: number) => {
    // TODO: Implement image removal
    console.log(`Removing image ${index}`);
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case "in_stock":
        return "bg-green-100 text-green-800";
      case "made_to_order":
        return "bg-blue-100 text-blue-800";
      case "out_of_stock":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Material requirements handlers
  const handleAddMaterial = () => {
    if (
      !newMaterial.materialName ||
      !newMaterial.unit ||
      newMaterial.requiredQuantity <= 0
    ) {
      return;
    }

    const updatedRequirements = [
      ...product.materialRequirements,
      {
        ...newMaterial,
        materialId: `rm${Date.now()}`, // Generate a temporary ID
      },
    ];

    setProduct((prev) => ({
      ...prev,
      materialRequirements: updatedRequirements,
    }));

    // Reset form
    setNewMaterial({
      materialId: "",
      materialName: "",
      requiredQuantity: 0,
      unit: "",
      isOptional: false,
      notes: "",
    });
    setIsAddingMaterial(false);
  };

  const handleEditMaterial = (index: number) => {
    const material = product.materialRequirements[index];
    setNewMaterial({
      ...material,
      notes: material.notes || "", // Handle optional notes field
    });
    setEditingMaterialIndex(index);
    setIsAddingMaterial(true);
  };

  const handleUpdateMaterial = () => {
    if (editingMaterialIndex === null) return;

    const updatedRequirements = [...product.materialRequirements];
    updatedRequirements[editingMaterialIndex] = newMaterial;

    setProduct((prev) => ({
      ...prev,
      materialRequirements: updatedRequirements,
    }));

    // Reset form
    setNewMaterial({
      materialId: "",
      materialName: "",
      requiredQuantity: 0,
      unit: "",
      isOptional: false,
      notes: "",
    });
    setEditingMaterialIndex(null);
    setIsAddingMaterial(false);
  };

  const handleDeleteMaterial = (index: number) => {
    setDeletingMaterialIndex(index);
  };

  const confirmDeleteMaterial = () => {
    if (deletingMaterialIndex === null) return;

    const updatedRequirements = product.materialRequirements.filter(
      (_, i) => i !== deletingMaterialIndex
    );
    setProduct((prev) => ({
      ...prev,
      materialRequirements: updatedRequirements,
    }));
    setDeletingMaterialIndex(null);
  };

  const cancelDeleteMaterial = () => {
    setDeletingMaterialIndex(null);
  };

  const handleCancelMaterial = () => {
    setNewMaterial({
      materialId: "",
      materialName: "",
      requiredQuantity: 0,
      unit: "",
      isOptional: false,
      notes: "",
    });
    setEditingMaterialIndex(null);
    setIsAddingMaterial(false);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title={product.name}
        description="Product details and settings"
        backHref="/merchant/inventory"
        actions={
          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button onClick={handleSave}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </>
            ) : (
              <>
                <Link href={`/merchant/inventory/${productId}/manufacturing`}>
                  <Button variant="outline">
                    <Settings className="mr-2 h-4 w-4" />
                    Manufacturing Phases
                  </Button>
                </Link>
                <Button onClick={() => setIsEditing(true)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Product
                </Button>
              </>
            )}
          </div>
        }
      />

      {/* Status Badges */}
      <div className="flex space-x-2">
        <Badge variant={product.isActive ? "default" : "secondary"}>
          {product.isActive ? "Active" : "Inactive"}
        </Badge>
        <Badge className={getStockStatusColor(product.stockStatus)}>
          {stockStatuses.find((s) => s.value === product.stockStatus)?.label}
        </Badge>
        <Badge variant="outline" className="capitalize">
          {product.category}
        </Badge>
      </div>

      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Product Details</TabsTrigger>
          <TabsTrigger value="materials">Material Requirements</TabsTrigger>
          <TabsTrigger value="images">Images</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name</Label>
                  {isEditing ? (
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) =>
                        handleInputChange("name", e.target.value)
                      }
                    />
                  ) : (
                    <p className="text-sm">{product.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  {isEditing ? (
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) =>
                        handleInputChange("description", e.target.value)
                      }
                      rows={4}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      {product.description}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Price</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={formData.price}
                        onChange={(e) =>
                          handleInputChange("price", parseFloat(e.target.value))
                        }
                      />
                    ) : (
                      <p className="text-sm font-medium">
                        {product.price.toLocaleString("en-ZA", {
                          style: "currency",
                          currency: "ZAR",
                        })}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Manufacturing Time</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        value={formData.manufacturingTime}
                        onChange={(e) =>
                          handleInputChange(
                            "manufacturingTime",
                            parseInt(e.target.value)
                          )
                        }
                      />
                    ) : (
                      <p className="text-sm">
                        {product.manufacturingTime} days
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Category</Label>
                    {isEditing ? (
                      <Select
                        value={formData.category}
                        onValueChange={(value) =>
                          handleInputChange("category", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.slug}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm capitalize">{product.category}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Stock Status</Label>
                    {isEditing ? (
                      <Select
                        value={formData.stockStatus}
                        onValueChange={(value) =>
                          handleInputChange("stockStatus", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {stockStatuses.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm">
                        {
                          stockStatuses.find(
                            (s) => s.value === product.stockStatus
                          )?.label
                        }
                      </p>
                    )}
                  </div>
                </div>

                {/* Manufacturing Options */}
                {isEditing && (
                  <div className="space-y-4 pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="applyGlobalPhases"
                        checked={formData.applyGlobalPhases || false}
                        onCheckedChange={(checked) =>
                          handleInputChange("applyGlobalPhases", checked)
                        }
                      />
                      <Label
                        htmlFor="applyGlobalPhases"
                        className="text-sm font-medium"
                      >
                        Apply global manufacturing phases to this product
                      </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      When enabled, this product will automatically inherit all
                      global manufacturing phases defined in your merchant
                      settings.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Specifications */}
            <Card>
              <CardHeader>
                <CardTitle>Specifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(product.specifications).map(
                    ([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-sm font-medium">{key}:</span>
                        <span className="text-sm text-muted-foreground">
                          {value}
                        </span>
                      </div>
                    )
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Product Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Product Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">12</p>
                  <p className="text-sm text-muted-foreground">Total Orders</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">8</p>
                  <p className="text-sm text-muted-foreground">Active Groups</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">
                    {(15599).toLocaleString("en-ZA", {
                      style: "currency",
                      currency: "ZAR",
                    })}
                  </p>
                  <p className="text-sm text-muted-foreground">Total Revenue</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">4.9</p>
                  <p className="text-sm text-muted-foreground">Avg Rating</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Product Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-sm text-muted-foreground">
                    {product.createdAt.toLocaleDateString("en-ZA", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Last Updated</p>
                  <p className="text-sm text-muted-foreground">
                    {product.updatedAt.toLocaleDateString("en-ZA", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Product ID</p>
                  <p className="text-sm text-muted-foreground font-mono">
                    {product.id}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="materials" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Material Requirements</CardTitle>
              <CardDescription>
                Define which materials are required to manufacture this product
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Add/Edit Material Form */}
                {isAddingMaterial && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {editingMaterialIndex !== null ? "Edit" : "Add"}{" "}
                        Material Requirement
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="materialName">Material Name *</Label>
                          <Input
                            id="materialName"
                            value={newMaterial.materialName}
                            onChange={(e) =>
                              setNewMaterial((prev) => ({
                                ...prev,
                                materialName: e.target.value,
                              }))
                            }
                            placeholder="e.g., Premium Black Granite"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="unit">Unit *</Label>
                          <Select
                            value={newMaterial.unit}
                            onValueChange={(value) =>
                              setNewMaterial((prev) => ({
                                ...prev,
                                unit: value,
                              }))
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select unit" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="m²">
                                Square meters (m²)
                              </SelectItem>
                              <SelectItem value="m³">
                                Cubic meters (m³)
                              </SelectItem>
                              <SelectItem value="kg">Kilograms (kg)</SelectItem>
                              <SelectItem value="g">Grams (g)</SelectItem>
                              <SelectItem value="L">Liters (L)</SelectItem>
                              <SelectItem value="ml">
                                Milliliters (ml)
                              </SelectItem>
                              <SelectItem value="pcs">Pieces (pcs)</SelectItem>
                              <SelectItem value="set">Set</SelectItem>
                              <SelectItem value="box">Box</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="requiredQuantity">
                            Required Quantity *
                          </Label>
                          <Input
                            id="requiredQuantity"
                            type="number"
                            step="0.01"
                            min="0"
                            value={newMaterial.requiredQuantity}
                            onChange={(e) =>
                              setNewMaterial((prev) => ({
                                ...prev,
                                requiredQuantity:
                                  parseFloat(e.target.value) || 0,
                              }))
                            }
                          />
                        </div>
                        <div className="space-y-2 flex items-end">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="isOptional"
                              checked={newMaterial.isOptional}
                              onCheckedChange={(checked) =>
                                setNewMaterial((prev) => ({
                                  ...prev,
                                  isOptional: !!checked,
                                }))
                              }
                            />
                            <Label htmlFor="isOptional">
                              Optional material
                            </Label>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="notes">Notes</Label>
                        <Textarea
                          id="notes"
                          value={newMaterial.notes}
                          onChange={(e) =>
                            setNewMaterial((prev) => ({
                              ...prev,
                              notes: e.target.value,
                            }))
                          }
                          placeholder="Additional notes about this material..."
                          rows={2}
                        />
                      </div>

                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          onClick={handleCancelMaterial}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={
                            editingMaterialIndex !== null
                              ? handleUpdateMaterial
                              : handleAddMaterial
                          }
                        >
                          {editingMaterialIndex !== null ? "Update" : "Add"}{" "}
                          Material
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Material Requirements List */}
                {product.materialRequirements.map((requirement, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">
                          {requirement.materialName}
                        </h4>
                        {requirement.isOptional && (
                          <Badge variant="outline">Optional</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {requirement.requiredQuantity} {requirement.unit}
                        {requirement.notes && ` • ${requirement.notes}`}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditMaterial(index)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteMaterial(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                {product.materialRequirements.length === 0 &&
                  !isAddingMaterial && (
                    <div className="text-center py-8">
                      <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">
                        No materials defined
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        Add material requirements to enable automatic job card
                        generation
                      </p>
                      <Button onClick={() => setIsAddingMaterial(true)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Material Requirement
                      </Button>
                    </div>
                  )}

                {product.materialRequirements.length > 0 &&
                  !isAddingMaterial && (
                    <div className="flex justify-between items-center pt-4 border-t">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {product.materialRequirements.length} material
                          {product.materialRequirements.length !== 1
                            ? "s"
                            : ""}{" "}
                          defined
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        onClick={() => setIsAddingMaterial(true)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Material
                      </Button>
                    </div>
                  )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="images" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Product Images</CardTitle>
              <CardDescription>
                Manage product images and set the main image. Hover over images
                to see options.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {product.images.map((image, index) => (
                  <div
                    key={index}
                    className="aspect-square relative overflow-hidden rounded-lg border group"
                  >
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="object-cover w-full h-full"
                    />

                    {/* Main Image Badge */}
                    {index === mainImageIndex && (
                      <Badge className="absolute top-2 left-2 bg-green-500 hover:bg-green-600">
                        <Star className="mr-1 h-3 w-3 fill-current" />
                        Main Image
                      </Badge>
                    )}

                    {/* Image Controls - Show on hover */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                      {index !== mainImageIndex && (
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => handleSetMainImage(index)}
                          className="text-xs"
                        >
                          <Star className="mr-1 h-3 w-3" />
                          Set as Main
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleRemoveImage(index)}
                        className="text-xs"
                      >
                        <Trash2 className="mr-1 h-3 w-3" />
                        Remove
                      </Button>
                    </div>
                  </div>
                ))}
                <div className="aspect-square border-2 border-dashed border-muted-foreground/25 rounded-lg flex items-center justify-center">
                  <Button variant="outline">
                    <Package className="mr-2 h-4 w-4" />
                    Add Image
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Product Analytics</CardTitle>
              <CardDescription>
                Performance metrics and insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Analytics dashboard coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
