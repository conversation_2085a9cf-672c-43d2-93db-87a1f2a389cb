"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  ChevronLeft,
  Plus,
  Save,
  Edit,
  Trash2,
  GripVertical,
  Clock,
  Settings,
} from "lucide-react";
import { ManufacturingPhase, MerchantInventoryItem } from "@/types/merchant";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data - replace with actual API calls
const mockProduct: MerchantInventoryItem = {
  id: "inv1",
  merchantId: "m1",
  name: "Classic Granite Memorial Stone",
  description:
    "Traditional granite memorial stone with elegant design and premium finish.",
  price: 1299.99,
  images: ["/images/stone-1.jpg"],
  category: "traditional",
  specifications: {},
  manufacturingTime: 21,
  manufacturingPhases: [],
  materialRequirements: [
    {
      materialId: "rm1",
      materialName: "Premium Black Granite",
      requiredQuantity: 2.5,
      unit: "m²",
      isOptional: false,
      notes: "Main stone material",
    },
    {
      materialId: "rm2",
      materialName: "Engraving Tools",
      requiredQuantity: 1,
      unit: "set",
      isOptional: false,
      notes: "For custom engraving work",
    },
  ],
  isActive: true,
  stockStatus: "made_to_order",
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-15"),
};

const mockPhases: ManufacturingPhase[] = [
  {
    id: "ph1",
    productId: "inv1",
    merchantId: "m1",
    name: "Design Approval",
    description: "Customer approves final design and specifications",
    estimatedDuration: 2,
    order: 1,
    isRequired: true,
    isGlobal: false,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "ph2",
    productId: "inv1",
    merchantId: "m1",
    name: "Stone Selection & Preparation",
    description: "Select and prepare granite stone for cutting",
    estimatedDuration: 3,
    order: 2,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
  },
  {
    id: "ph3",
    productId: "inv1",
    merchantId: "m1",
    name: "Stone Cutting",
    description: "Cut stone to specified dimensions and shape",
    estimatedDuration: 5,
    order: 3,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
  },
  {
    id: "ph4",
    productId: "inv1",
    merchantId: "m1",
    name: "Engraving",
    description: "Engrave text and designs onto the stone",
    estimatedDuration: 7,
    order: 4,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
  },
  {
    id: "ph5",
    productId: "inv1",
    merchantId: "m1",
    name: "Final Polish",
    description: "Polish and finish the memorial stone",
    estimatedDuration: 4,
    order: 5,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
  },
];

export default function ProductManufacturingPhases() {
  const params = useParams();
  const productId = params.id as string;

  const [product, setProduct] = useState<MerchantInventoryItem>(mockProduct);
  const [phases, setPhases] = useState<ManufacturingPhase[]>(mockPhases);
  const [isAddingPhase, setIsAddingPhase] = useState(false);
  const [editingPhase, setEditingPhase] = useState<string | null>(null);

  const [newPhase, setNewPhase] = useState({
    name: "",
    description: "",
    estimatedDuration: 1,
    isRequired: true,
  });

  const totalDuration = phases.reduce(
    (sum, phase) => sum + phase.estimatedDuration,
    0
  );

  const handleAddPhase = () => {
    const phase: ManufacturingPhase = {
      id: `ph${Date.now()}`,
      productId: productId,
      merchantId: "m1",
      name: newPhase.name,
      description: newPhase.description,
      estimatedDuration: newPhase.estimatedDuration,
      order: phases.length + 1,
      isRequired: newPhase.isRequired,
      isGlobal: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setPhases([...phases, phase]);
    setNewPhase({
      name: "",
      description: "",
      estimatedDuration: 1,
      isRequired: true,
    });
    setIsAddingPhase(false);
  };

  const handleDeletePhase = (phaseId: string) => {
    setPhases(phases.filter((p) => p.id !== phaseId));
  };

  const handleSavePhases = () => {
    // TODO: Implement actual phase saving
    console.log("Saving phases:", phases);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Manufacturing Phases"
        description={`Setup manufacturing process for ${product.name}`}
        backHref={`/merchant/inventory/${productId}`}
        actions={
          <div className="flex space-x-2">
            <Link href="/merchant/manufacturing/phases">
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Global Phases
              </Button>
            </Link>
            <Button variant="outline" onClick={() => setIsAddingPhase(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Phase
            </Button>
            <Button onClick={handleSavePhases}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        }
      />

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Manufacturing Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{phases.length}</p>
              <p className="text-sm text-muted-foreground">Total Phases</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{totalDuration}</p>
              <p className="text-sm text-muted-foreground">Total Days</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">
                {phases.filter((p) => p.isRequired).length}
              </p>
              <p className="text-sm text-muted-foreground">Required Phases</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add New Phase */}
      {isAddingPhase && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Phase</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phaseName">Phase Name</Label>
                <Input
                  id="phaseName"
                  value={newPhase.name}
                  onChange={(e) =>
                    setNewPhase((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="e.g., Stone Cutting"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="duration">Duration (days)</Label>
                <Input
                  id="duration"
                  type="number"
                  min="1"
                  value={newPhase.estimatedDuration}
                  onChange={(e) =>
                    setNewPhase((prev) => ({
                      ...prev,
                      estimatedDuration: parseInt(e.target.value),
                    }))
                  }
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phaseDescription">Description</Label>
              <Textarea
                id="phaseDescription"
                value={newPhase.description}
                onChange={(e) =>
                  setNewPhase((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Describe what happens in this phase..."
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddingPhase(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddPhase}>Add Phase</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Manufacturing Phases */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Manufacturing Phases</h3>

        {phases.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground mb-4">
                No manufacturing phases defined yet.
              </p>
              <Button onClick={() => setIsAddingPhase(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add First Phase
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-3">
            {phases.map((phase, index) => (
              <Card key={phase.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                        <Badge variant="outline">Phase {index + 1}</Badge>
                      </div>

                      <div className="flex-1">
                        <h4 className="font-semibold">{phase.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {phase.description}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>{phase.estimatedDuration} days</span>
                      </div>

                      {phase.isRequired && (
                        <Badge variant="secondary">Required</Badge>
                      )}

                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingPhase(phase.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeletePhase(phase.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Phase Timeline Preview */}
      {phases.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Manufacturing Timeline Preview</CardTitle>
            <CardDescription>
              Visual representation of the manufacturing process
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {phases.map((phase, index) => (
                <div key={phase.id} className="flex items-center space-x-4">
                  <div className="flex flex-col items-center">
                    <div className="w-3 h-3 rounded-full bg-primary" />
                    {index < phases.length - 1 && (
                      <div className="w-px h-8 bg-border mt-2" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{phase.name}</h4>
                      <span className="text-sm text-muted-foreground">
                        {phase.estimatedDuration} days
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {phase.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
