"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Phone,
  Mail,
  MapPin,
  Star,
  Clock,
  CreditCard,
  Edit,
  Package2,
  TrendingUp,
  Calendar,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterialSupplier, RawMaterial } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock supplier data
const mockSupplier: RawMaterialSupplier = {
  id: "sup1",
  name: "Cape Granite Suppliers",
  contactPerson: "<PERSON>",
  email: "<EMAIL>",
  phone: "+27 21 555 0123",
  address: "15 Industrial Road, Cape Town, 7405",
  rating: 4.8,
  isActive: true,
  paymentTerms: "30 days",
  deliveryTime: 5,
  createdAt: new Date("2023-01-15"),
  updatedAt: new Date("2024-01-20"),
};

// Mock materials supplied by this supplier
const mockSuppliedMaterials: RawMaterial[] = [
  {
    id: "rm1",
    merchantId: "m1",
    name: "Premium Black Granite",
    description: "High-quality black granite from Mpumalanga quarries",
    category: "stone",
    unit: "m²",
    costPerUnit: convertToZAR(450.00),
    currentStock: 25.5,
    minimumStock: 10.0,
    maximumStock: 100.0,
    supplierId: "sup1",
    specifications: {
      Origin: "Mpumalanga, South Africa",
      Density: "2.7 g/cm³",
      "Compressive Strength": "200 MPa",
    },
    storageLocation: "Warehouse A - Section 1",
    isActive: true,
    lastRestocked: new Date("2024-01-15"),
    createdAt: new Date("2023-06-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "rm3",
    merchantId: "m1",
    name: "Stone Cutting Blades",
    description: "Diamond-tipped cutting blades for granite processing",
    category: "tools",
    unit: "pieces",
    costPerUnit: convertToZAR(125.00),
    currentStock: 8,
    minimumStock: 5,
    maximumStock: 25,
    supplierId: "sup1",
    specifications: {
      Diameter: "350mm",
      "Blade Type": "Diamond Segmented",
      "Max RPM": "3,500",
    },
    storageLocation: "Tool Room - Shelf B",
    isActive: true,
    lastRestocked: new Date("2024-01-10"),
    createdAt: new Date("2023-08-01"),
    updatedAt: new Date("2024-01-10"),
  },
];

// Mock order history
const mockOrderHistory = [
  {
    id: "ord1",
    date: new Date("2024-01-15"),
    materials: ["Premium Black Granite"],
    totalAmount: convertToZAR(11475.00),
    status: "delivered",
    deliveryDate: new Date("2024-01-20"),
  },
  {
    id: "ord2",
    date: new Date("2024-01-10"),
    materials: ["Stone Cutting Blades"],
    totalAmount: convertToZAR(1250.00),
    status: "delivered",
    deliveryDate: new Date("2024-01-13"),
  },
  {
    id: "ord3",
    date: new Date("2023-12-20"),
    materials: ["Premium Black Granite", "Stone Cutting Blades"],
    totalAmount: convertToZAR(5625.00),
    status: "delivered",
    deliveryDate: new Date("2023-12-27"),
  },
];

export default function SupplierDetails() {
  const params = useParams();
  const supplierId = params.id as string;
  const [supplier] = useState<RawMaterialSupplier>(mockSupplier);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "fill-yellow-400 text-yellow-400"
            : "text-gray-300"
        }`}
      />
    ));
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600";
    if (rating >= 4.0) return "text-yellow-600";
    return "text-red-600";
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStockStatus = (material: RawMaterial) => {
    if (material.currentStock <= material.minimumStock) {
      return { status: "Low Stock", color: "bg-red-100 text-red-800", icon: AlertTriangle };
    } else {
      return { status: "Normal", color: "bg-green-100 text-green-800", icon: CheckCircle };
    }
  };

  const totalOrderValue = mockOrderHistory.reduce((sum, order) => sum + order.totalAmount, 0);
  const averageOrderValue = totalOrderValue / mockOrderHistory.length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title={supplier.name}
        description="Supplier details and relationship management"
        showBackButton
        actions={
          <div className="flex space-x-2">
            <Button variant="outline">
              <Phone className="mr-2 h-4 w-4" />
              Call
            </Button>
            <Button variant="outline">
              <Mail className="mr-2 h-4 w-4" />
              Email
            </Button>
            <Link href={`/merchant/raw-materials/suppliers/${supplierId}/edit`}>
              <Button>
                <Edit className="mr-2 h-4 w-4" />
                Edit Supplier
              </Button>
            </Link>
          </div>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getRatingColor(supplier.rating)}`}>
              {supplier.rating}
            </div>
            <p className="text-xs text-muted-foreground">out of 5.0</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Materials Supplied</CardTitle>
            <Package2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockSuppliedMaterials.length}</div>
            <p className="text-xs text-muted-foreground">active materials</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockOrderHistory.length}</div>
            <p className="text-xs text-muted-foreground">completed orders</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatZAR(totalOrderValue)}</div>
            <p className="text-xs text-muted-foreground">all-time orders</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="materials">Materials</TabsTrigger>
          <TabsTrigger value="orders">Order History</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{supplier.email}</p>
                    <p className="text-sm text-muted-foreground">Email</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{supplier.phone}</p>
                    <p className="text-sm text-muted-foreground">Phone</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="font-medium">{supplier.address}</p>
                    <p className="text-sm text-muted-foreground">Address</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="h-5 w-5 flex items-center justify-center">
                    <span className="text-sm font-medium">👤</span>
                  </div>
                  <div>
                    <p className="font-medium">{supplier.contactPerson}</p>
                    <p className="text-sm text-muted-foreground">Contact Person</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Business Terms */}
            <Card>
              <CardHeader>
                <CardTitle>Business Terms</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Payment Terms</span>
                  <div className="flex items-center gap-1">
                    <CreditCard className="h-4 w-4" />
                    <span className="font-medium">{supplier.paymentTerms}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Delivery Time</span>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span className="font-medium">{supplier.deliveryTime} days</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Rating</span>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      {renderStars(supplier.rating)}
                    </div>
                    <span className={`font-medium ${getRatingColor(supplier.rating)}`}>
                      {supplier.rating}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <Badge variant={supplier.isActive ? "default" : "secondary"}>
                    {supplier.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Partnership Since</span>
                  <span className="font-medium">
                    {supplier.createdAt.toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="materials" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {mockSuppliedMaterials.map((material) => {
              const stockStatus = getStockStatus(material);
              const StockIcon = stockStatus.icon;
              
              return (
                <Card key={material.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{material.name}</CardTitle>
                        <CardDescription className="line-clamp-2">
                          {material.description}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{material.category}</Badge>
                      <Badge className={stockStatus.color}>
                        <StockIcon className="mr-1 h-3 w-3" />
                        {stockStatus.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Current Stock</span>
                        <span className="font-medium">
                          {material.currentStock} {material.unit}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Cost per {material.unit}</span>
                        <span className="font-medium">{formatZAR(material.costPerUnit)}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Last Restocked</span>
                        <span className="font-medium">
                          {material.lastRestocked.toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <div className="space-y-4">
            {mockOrderHistory.map((order) => (
              <Card key={order.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">Order #{order.id.toUpperCase()}</CardTitle>
                      <CardDescription>
                        {order.date.toLocaleDateString()} • {order.materials.join(", ")}
                      </CardDescription>
                    </div>
                    <Badge className={getOrderStatusColor(order.status)}>
                      {order.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Order Date</p>
                        <p className="text-sm text-muted-foreground">
                          {order.date.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Delivered</p>
                        <p className="text-sm text-muted-foreground">
                          {order.deliveryDate?.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Total Amount</p>
                        <p className="text-sm text-muted-foreground">
                          {formatZAR(order.totalAmount)}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Order Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Orders</span>
                  <span className="font-medium">{mockOrderHistory.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Average Order Value</span>
                  <span className="font-medium">{formatZAR(averageOrderValue)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Order Value</span>
                  <span className="font-medium">{formatZAR(totalOrderValue)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">On-Time Delivery</span>
                  <span className="font-medium text-green-600">100%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Supplier Rating</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Overall Rating</span>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      {renderStars(supplier.rating)}
                    </div>
                    <span className={`font-medium ${getRatingColor(supplier.rating)}`}>
                      {supplier.rating}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Quality</span>
                  <span className="font-medium">Excellent</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Reliability</span>
                  <span className="font-medium">Very Good</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Communication</span>
                  <span className="font-medium">Excellent</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
