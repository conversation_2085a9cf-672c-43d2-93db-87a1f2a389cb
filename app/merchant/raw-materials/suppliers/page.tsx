"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Plus,
  Search,
  Truck,
  Eye,
  Edit,
  MoreHorizontal,
  Phone,
  Mail,
  MapPin,
  Star,
  Clock,
  CreditCard,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterialSupplier } from "@/types/merchant";

// Mock suppliers data
const mockSuppliers: RawMaterialSupplier[] = [
  {
    id: "sup1",
    name: "Cape Granite Suppliers",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "+27 21 555 0123",
    address: "15 Industrial Road, Cape Town, 7405",
    rating: 4.8,
    isActive: true,
    paymentTerms: "30 days",
    deliveryTime: 5,
    createdAt: new Date("2023-01-15"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "sup2",
    name: "Johannesburg Stone Works",
    contactPerson: "Sipho Mthembu",
    email: "<EMAIL>",
    phone: "+27 11 555 0456",
    address: "42 Mining Street, Johannesburg, 2001",
    rating: 4.6,
    isActive: true,
    paymentTerms: "15 days",
    deliveryTime: 3,
    createdAt: new Date("2023-03-10"),
    updatedAt: new Date("2024-01-18"),
  },
  {
    id: "sup3",
    name: "Durban Industrial Supplies",
    contactPerson: "Priya Patel",
    email: "<EMAIL>",
    phone: "+27 31 555 0789",
    address: "88 Industrial Avenue, Durban, 4001",
    rating: 4.4,
    isActive: true,
    paymentTerms: "45 days",
    deliveryTime: 7,
    createdAt: new Date("2023-05-20"),
    updatedAt: new Date("2024-01-12"),
  },
  {
    id: "sup4",
    name: "Pretoria Tool & Equipment",
    contactPerson: "Michael Botha",
    email: "<EMAIL>",
    phone: "+27 12 555 0321",
    address: "23 Workshop Street, Pretoria, 0001",
    rating: 4.2,
    isActive: false,
    paymentTerms: "30 days",
    deliveryTime: 4,
    createdAt: new Date("2023-08-05"),
    updatedAt: new Date("2023-12-15"),
  },
];

export default function SuppliersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");

  const filteredSuppliers = mockSuppliers.filter((supplier) => {
    const matchesSearch = 
      supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      selectedStatus === "all" ||
      (selectedStatus === "active" && supplier.isActive) ||
      (selectedStatus === "inactive" && !supplier.isActive);

    return matchesSearch && matchesStatus;
  });

  const activeSuppliers = mockSuppliers.filter(s => s.isActive).length;
  const averageRating = mockSuppliers.reduce((sum, s) => sum + s.rating, 0) / mockSuppliers.length;
  const averageDeliveryTime = mockSuppliers.reduce((sum, s) => sum + s.deliveryTime, 0) / mockSuppliers.length;

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600";
    if (rating >= 4.0) return "text-yellow-600";
    return "text-red-600";
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "fill-yellow-400 text-yellow-400"
            : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Suppliers"
        description="Manage your raw material suppliers and vendor relationships"
        showBackButton
        actions={
          <Link href="/merchant/raw-materials/suppliers/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Supplier
            </Button>
          </Link>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Suppliers</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockSuppliers.length}</div>
            <p className="text-xs text-muted-foreground">
              {activeSuppliers} active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageRating.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              Out of 5.0 stars
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Delivery Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(averageDeliveryTime)}</div>
            <p className="text-xs text-muted-foreground">
              Days average
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Suppliers</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeSuppliers}</div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search suppliers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={selectedStatus === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedStatus("all")}
          >
            All
          </Button>
          <Button
            variant={selectedStatus === "active" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedStatus("active")}
          >
            Active
          </Button>
          <Button
            variant={selectedStatus === "inactive" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedStatus("inactive")}
          >
            Inactive
          </Button>
        </div>
      </div>

      {/* Suppliers Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredSuppliers.map((supplier) => (
          <Card key={supplier.id} className="overflow-hidden">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{supplier.name}</CardTitle>
                  <CardDescription>
                    Contact: {supplier.contactPerson}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={supplier.isActive ? "default" : "secondary"}>
                    {supplier.isActive ? "Active" : "Inactive"}
                  </Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/merchant/raw-materials/suppliers/${supplier.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/merchant/raw-materials/suppliers/${supplier.id}/edit`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              
              {/* Rating */}
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  {renderStars(supplier.rating)}
                </div>
                <span className={`text-sm font-medium ${getRatingColor(supplier.rating)}`}>
                  {supplier.rating}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="truncate">{supplier.email}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{supplier.phone}</span>
                </div>
                <div className="flex items-start gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <span className="line-clamp-2">{supplier.address}</span>
                </div>
                
                <div className="pt-2 border-t space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Payment Terms</span>
                    <div className="flex items-center gap-1">
                      <CreditCard className="h-3 w-3" />
                      <span>{supplier.paymentTerms}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Delivery Time</span>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{supplier.deliveryTime} days</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredSuppliers.length === 0 && (
        <div className="text-center py-12">
          <Truck className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">No suppliers found</h3>
          <p className="text-muted-foreground">
            {searchTerm
              ? "Try adjusting your search terms"
              : "Get started by adding your first supplier"}
          </p>
          {!searchTerm && (
            <Link href="/merchant/raw-materials/suppliers/new">
              <Button className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                Add Supplier
              </Button>
            </Link>
          )}
        </div>
      )}
    </div>
  );
}
