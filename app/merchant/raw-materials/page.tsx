"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Plus,
  Search,
  Filter,
  Package2,
  Eye,
  Edit,
  MoreHorizontal,
  AlertTriangle,
  CheckCircle,
  Truck,
  DollarSign,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterial, RawMaterialSupplier } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock suppliers data
const mockSuppliers: RawMaterialSupplier[] = [
  {
    id: "sup1",
    name: "Cape Granite Suppliers",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "+27 21 555 0123",
    address: "15 Industrial Road, Cape Town, 7405",
    rating: 4.8,
    isActive: true,
    paymentTerms: "30 days",
    deliveryTime: 5,
    createdAt: new Date("2023-01-15"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "sup2",
    name: "Johannesburg Stone Works",
    contactPerson: "Sipho Mthembu",
    email: "<EMAIL>",
    phone: "+27 11 555 0456",
    address: "42 Mining Street, Johannesburg, 2001",
    rating: 4.6,
    isActive: true,
    paymentTerms: "15 days",
    deliveryTime: 3,
    createdAt: new Date("2023-03-10"),
    updatedAt: new Date("2024-01-18"),
  },
];

// Mock raw materials data
const mockRawMaterials: RawMaterial[] = [
  {
    id: "rm1",
    merchantId: "m1",
    name: "Premium Black Granite",
    description: "High-quality black granite from Mpumalanga quarries",
    category: "stone",
    unit: "m²",
    costPerUnit: convertToZAR(450.0),
    currentStock: 25.5,
    minimumStock: 10.0,
    maximumStock: 100.0,
    supplierId: "sup1",
    supplier: mockSuppliers[0],
    specifications: {
      Origin: "Mpumalanga, South Africa",
      Density: "2.7 g/cm³",
      "Compressive Strength": "200 MPa",
      "Water Absorption": "< 0.4%",
      Finish: "Rough Cut",
    },
    storageLocation: "Warehouse A - Section 1",
    isActive: true,
    lastRestocked: new Date("2024-01-15"),
    createdAt: new Date("2023-06-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "rm2",
    merchantId: "m1",
    name: "Grey Granite Slabs",
    description: "Durable grey granite suitable for memorial stones",
    category: "stone",
    unit: "m²",
    costPerUnit: convertToZAR(380.0),
    currentStock: 42.0,
    minimumStock: 15.0,
    maximumStock: 80.0,
    supplierId: "sup2",
    supplier: mockSuppliers[1],
    specifications: {
      Origin: "Northern Cape, South Africa",
      Density: "2.65 g/cm³",
      "Compressive Strength": "180 MPa",
      "Water Absorption": "< 0.5%",
      Finish: "Polished",
    },
    storageLocation: "Warehouse A - Section 2",
    isActive: true,
    lastRestocked: new Date("2024-01-20"),
    createdAt: new Date("2023-07-15"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "rm3",
    merchantId: "m1",
    name: "Stone Cutting Blades",
    description: "Diamond-tipped cutting blades for granite processing",
    category: "tools",
    unit: "pieces",
    costPerUnit: convertToZAR(125.0),
    currentStock: 8,
    minimumStock: 5,
    maximumStock: 25,
    supplierId: "sup1",
    supplier: mockSuppliers[0],
    specifications: {
      Diameter: "350mm",
      "Blade Type": "Diamond Segmented",
      "Max RPM": "3,500",
      "Suitable For": "Granite, Marble",
    },
    storageLocation: "Tool Room - Shelf B",
    isActive: true,
    lastRestocked: new Date("2024-01-10"),
    createdAt: new Date("2023-08-01"),
    updatedAt: new Date("2024-01-10"),
  },
  {
    id: "rm4",
    merchantId: "m1",
    name: "Stone Polishing Compound",
    description: "Professional-grade polishing compound for granite finishing",
    category: "consumables",
    unit: "kg",
    costPerUnit: convertToZAR(85.0),
    currentStock: 3.5,
    minimumStock: 5.0,
    maximumStock: 20.0,
    supplierId: "sup2",
    supplier: mockSuppliers[1],
    specifications: {
      Type: "Diamond Polishing Paste",
      Grit: "3000",
      "Container Size": "1kg",
      "Shelf Life": "24 months",
    },
    storageLocation: "Chemical Storage - Cabinet C",
    isActive: true,
    lastRestocked: new Date("2023-12-15"),
    expiryDate: new Date("2025-12-15"),
    createdAt: new Date("2023-09-01"),
    updatedAt: new Date("2023-12-15"),
  },
];

const materialCategories = [
  { value: "all", label: "All Categories" },
  { value: "stone", label: "Stone Materials" },
  { value: "tools", label: "Tools & Equipment" },
  { value: "consumables", label: "Consumables" },
  { value: "hardware", label: "Hardware" },
];

export default function RawMaterialsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [stockFilter, setStockFilter] = useState("all");

  const filteredMaterials = mockRawMaterials.filter((material) => {
    const matchesSearch = material.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" || material.category === selectedCategory;
    const matchesStatus =
      selectedStatus === "all" ||
      (selectedStatus === "active" && material.isActive) ||
      (selectedStatus === "inactive" && !material.isActive);

    let matchesStock = true;
    if (stockFilter === "low") {
      matchesStock = material.currentStock <= material.minimumStock;
    } else if (stockFilter === "normal") {
      matchesStock =
        material.currentStock > material.minimumStock &&
        material.currentStock < material.maximumStock * 0.8;
    } else if (stockFilter === "high") {
      matchesStock = material.currentStock >= material.maximumStock * 0.8;
    }

    return matchesSearch && matchesCategory && matchesStatus && matchesStock;
  });

  const lowStockItems = mockRawMaterials.filter(
    (material) => material.currentStock <= material.minimumStock
  ).length;

  const totalValue = filteredMaterials.reduce(
    (sum, material) => sum + material.currentStock * material.costPerUnit,
    0
  );

  const activeSuppliers = new Set(
    mockRawMaterials.map((material) => material.supplierId)
  ).size;

  const getStockStatus = (material: RawMaterial) => {
    if (material.currentStock <= material.minimumStock) {
      return {
        status: "Low Stock",
        color: "bg-red-100 text-red-800",
        icon: AlertTriangle,
      };
    } else if (material.currentStock >= material.maximumStock * 0.8) {
      return {
        status: "High Stock",
        color: "bg-blue-100 text-blue-800",
        icon: CheckCircle,
      };
    } else {
      return {
        status: "Normal",
        color: "bg-green-100 text-green-800",
        icon: CheckCircle,
      };
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Raw Materials"
        description="Manage inventory of raw materials and supplies for manufacturing"
        actions={
          <div className="flex gap-2">
            <Link href="/merchant/raw-materials/suppliers">
              <Button variant="outline">
                <Truck className="mr-2 h-4 w-4" />
                Suppliers
              </Button>
            </Link>
            <Link href="/merchant/raw-materials/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Material
              </Button>
            </Link>
          </div>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Materials
            </CardTitle>
            <Package2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredMaterials.length}</div>
            <p className="text-xs text-muted-foreground">
              Active inventory items
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Low Stock Items
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {lowStockItems}
            </div>
            <p className="text-xs text-muted-foreground">Need restocking</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatZAR(totalValue)}</div>
            <p className="text-xs text-muted-foreground">Current stock value</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Suppliers
            </CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeSuppliers}</div>
            <p className="text-xs text-muted-foreground">Verified suppliers</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search materials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {materialCategories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={stockFilter} onValueChange={setStockFilter}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Stock" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Stock</SelectItem>
              <SelectItem value="low">Low Stock</SelectItem>
              <SelectItem value="normal">Normal</SelectItem>
              <SelectItem value="high">High Stock</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Materials Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredMaterials.map((material) => {
          const stockStatus = getStockStatus(material);
          const StockIcon = stockStatus.icon;

          return (
            <Card key={material.id} className="overflow-hidden">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{material.name}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {material.description}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/merchant/raw-materials/${material.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link
                          href={`/merchant/raw-materials/${material.id}/stock`}
                        >
                          <Package2 className="mr-2 h-4 w-4" />
                          Manage Stock
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link
                          href={`/merchant/raw-materials/${material.id}/edit`}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{material.category}</Badge>
                  <Badge className={stockStatus.color}>
                    <StockIcon className="mr-1 h-3 w-3" />
                    {stockStatus.status}
                  </Badge>
                  {!material.isActive && (
                    <Badge variant="secondary">Inactive</Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Current Stock
                    </span>
                    <span className="font-semibold">
                      {material.currentStock} {material.unit}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Cost per {material.unit}
                    </span>
                    <span className="font-semibold">
                      {formatZAR(material.costPerUnit)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Total Value
                    </span>
                    <span className="font-semibold">
                      {formatZAR(material.currentStock * material.costPerUnit)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Supplier
                    </span>
                    <span className="text-sm">{material.supplier?.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Location
                    </span>
                    <span className="text-sm">{material.storageLocation}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredMaterials.length === 0 && (
        <div className="text-center py-12">
          <Package2 className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">No materials found</h3>
          <p className="text-muted-foreground">
            {searchTerm || selectedCategory !== "all"
              ? "Try adjusting your filters"
              : "Get started by adding your first raw material"}
          </p>
          {!searchTerm && selectedCategory === "all" && (
            <Link href="/merchant/raw-materials/new">
              <Button className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                Add Material
              </Button>
            </Link>
          )}
        </div>
      )}
    </div>
  );
}
