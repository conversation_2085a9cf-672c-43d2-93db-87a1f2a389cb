"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Save,
  ArrowLeft,
  Package,
  Wrench,
  AlertTriangle,
  User,
  FileText,
  Calendar,
} from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterial } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock material data
const mockMaterial: RawMaterial = {
  id: "rm1",
  merchantId: "m1",
  name: "Premium Black Granite",
  description: "High-quality black granite from Mpumalanga quarries",
  category: "stone",
  unit: "m²",
  costPerUnit: convertToZAR(450.00),
  currentStock: 25.5,
  minimumStock: 10.0,
  maximumStock: 100.0,
  supplierId: "sup1",
  specifications: {},
  storageLocation: "Warehouse A - Section 1",
  isActive: true,
  lastRestocked: new Date("2024-01-15"),
  createdAt: new Date("2023-06-01"),
  updatedAt: new Date("2024-01-15"),
};

const usageTypes = [
  { value: "manufacturing", label: "Manufacturing", icon: "🏭" },
  { value: "waste", label: "Waste/Scrap", icon: "🗑️" },
  { value: "sample", label: "Sample/Testing", icon: "🔬" },
  { value: "maintenance", label: "Maintenance", icon: "🔧" },
  { value: "other", label: "Other", icon: "📦" },
];

// Mock job references for manufacturing
const mockJobReferences = [
  { value: "JOB-2024-001", label: "JOB-2024-001 - Classic Memorial Stone" },
  { value: "JOB-2024-002", label: "JOB-2024-002 - Custom Engraved Memorial" },
  { value: "JOB-2024-003", label: "JOB-2024-003 - Heritage Family Stone" },
  { value: "JOB-2024-004", label: "JOB-2024-004 - Premium Monument" },
];

export default function RecordUsage() {
  const params = useParams();
  const router = useRouter();
  const materialId = params.id as string;
  const [material] = useState<RawMaterial>(mockMaterial);

  const [formData, setFormData] = useState({
    quantity: "",
    usageType: "",
    reference: "",
    description: "",
    usedBy: "",
    usedAt: new Date().toISOString().split('T')[0], // Today's date
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const quantity = parseFloat(formData.quantity);
    if (quantity > material.currentStock) {
      alert(`Cannot use ${quantity} ${material.unit}. Only ${material.currentStock} ${material.unit} available in stock.`);
      return;
    }

    // TODO: Implement actual usage recording
    console.log("Recording usage:", {
      materialId,
      quantity: -quantity, // Negative for usage
      usageType: formData.usageType,
      reference: formData.reference,
      description: formData.description,
      usedBy: formData.usedBy,
      usedAt: new Date(formData.usedAt),
      balanceAfter: material.currentStock - quantity,
    });

    router.push(`/merchant/raw-materials/${materialId}/stock`);
  };

  const handleCancel = () => {
    router.push(`/merchant/raw-materials/${materialId}/stock`);
  };

  const remainingStock = material.currentStock - parseFloat(formData.quantity || "0");
  const isLowStock = remainingStock <= material.minimumStock;
  const isOverStock = parseFloat(formData.quantity || "0") > material.currentStock;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Record Material Usage"
        description={`Record usage for ${material.name}`}
        showBackButton
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={isOverStock || !formData.quantity || !formData.usageType}>
              <Save className="mr-2 h-4 w-4" />
              Record Usage
            </Button>
          </div>
        }
      />

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid gap-8 lg:grid-cols-3">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Material Info */}
            <Card>
              <CardHeader>
                <CardTitle>Material Information</CardTitle>
                <CardDescription>
                  Current stock and material details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label>Material Name</Label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="font-medium">{material.name}</p>
                      <p className="text-sm text-muted-foreground">{material.description}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Current Stock</Label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="font-medium text-lg">
                        {material.currentStock} {material.unit}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Value: {formatZAR(material.currentStock * material.costPerUnit)}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Storage Location</Label>
                  <div className="p-3 bg-muted rounded-md">
                    <p className="font-medium">{material.storageLocation}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Usage Details */}
            <Card>
              <CardHeader>
                <CardTitle>Usage Details</CardTitle>
                <CardDescription>
                  Enter the details of material usage
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">Quantity Used *</Label>
                    <div className="relative">
                      <Package className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="quantity"
                        type="number"
                        step="0.01"
                        min="0.01"
                        max={material.currentStock}
                        placeholder={`0.00 ${material.unit}`}
                        value={formData.quantity}
                        onChange={(e) => handleInputChange("quantity", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                    {isOverStock && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertTriangle className="h-3 w-3" />
                        Exceeds available stock
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="usageType">Usage Type *</Label>
                    <div className="relative">
                      <Wrench className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground z-10" />
                      <Select
                        value={formData.usageType}
                        onValueChange={(value) => handleInputChange("usageType", value)}
                      >
                        <SelectTrigger className="pl-10">
                          <SelectValue placeholder="Select usage type" />
                        </SelectTrigger>
                        <SelectContent>
                          {usageTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <span className="flex items-center gap-2">
                                <span>{type.icon}</span>
                                {type.label}
                              </span>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="reference">Reference/Job Number</Label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground z-10" />
                      {formData.usageType === "manufacturing" ? (
                        <Select
                          value={formData.reference}
                          onValueChange={(value) => handleInputChange("reference", value)}
                        >
                          <SelectTrigger className="pl-10">
                            <SelectValue placeholder="Select job reference" />
                          </SelectTrigger>
                          <SelectContent>
                            {mockJobReferences.map((job) => (
                              <SelectItem key={job.value} value={job.value}>
                                {job.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <Input
                          id="reference"
                          placeholder="e.g., MAINT-2024-001, SAMPLE-001"
                          value={formData.reference}
                          onChange={(e) => handleInputChange("reference", e.target.value)}
                          className="pl-10"
                        />
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="usedBy">Used By *</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="usedBy"
                        placeholder="e.g., John Smith, Production Team"
                        value={formData.usedBy}
                        onChange={(e) => handleInputChange("usedBy", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="usedAt">Usage Date *</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="usedAt"
                      type="date"
                      value={formData.usedAt}
                      onChange={(e) => handleInputChange("usedAt", e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description/Notes *</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe what the material was used for, any specific details..."
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    rows={3}
                    required
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Stock Impact */}
            <Card>
              <CardHeader>
                <CardTitle>Stock Impact</CardTitle>
                <CardDescription>
                  How this usage will affect stock levels
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Current Stock</span>
                  <span className="font-medium">
                    {material.currentStock} {material.unit}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Usage Amount</span>
                  <span className="font-medium text-red-600">
                    -{formData.quantity || "0"} {material.unit}
                  </span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Remaining Stock</span>
                    <span className={`font-bold ${isLowStock ? 'text-red-600' : 'text-green-600'}`}>
                      {remainingStock.toFixed(2)} {material.unit}
                    </span>
                  </div>
                  {isLowStock && remainingStock >= 0 && (
                    <div className="flex items-center gap-1 mt-1">
                      <AlertTriangle className="h-3 w-3 text-red-600" />
                      <span className="text-xs text-red-600">Below minimum stock level</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Remaining Value</span>
                  <span className="font-medium">
                    {formatZAR(remainingStock * material.costPerUnit)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Usage Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Usage Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Material:</span>
                  <span className="font-medium truncate ml-2">{material.name}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Quantity:</span>
                  <span className="font-medium">{formData.quantity || "0"} {material.unit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Type:</span>
                  <span className="font-medium">
                    {usageTypes.find(t => t.value === formData.usageType)?.label || "Not selected"}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Reference:</span>
                  <span className="font-medium truncate ml-2">{formData.reference || "None"}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Used By:</span>
                  <span className="font-medium truncate ml-2">{formData.usedBy || "Not set"}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Date:</span>
                  <span className="font-medium">
                    {formData.usedAt ? new Date(formData.usedAt).toLocaleDateString() : "Not set"}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Guidelines */}
            <Card>
              <CardHeader>
                <CardTitle>Guidelines</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-muted-foreground">
                <p>• Record usage immediately after consumption</p>
                <p>• Include specific job references for manufacturing</p>
                <p>• Be accurate with quantities to maintain stock integrity</p>
                <p>• Add detailed notes for future reference</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
