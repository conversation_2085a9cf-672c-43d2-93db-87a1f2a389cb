"use client";

import { useState } from "react";
import { usePara<PERSON> } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Plus,
  Package,
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Truck,
  Wrench,
  Calendar,
  User,
  FileText,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterial, StockTransaction, PurchaseOrder } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock material data
const mockMaterial: RawMaterial = {
  id: "rm1",
  merchantId: "m1",
  name: "Premium Black Granite",
  description: "High-quality black granite from Mpumalanga quarries",
  category: "stone",
  unit: "m²",
  costPerUnit: convertToZAR(450.00),
  currentStock: 25.5,
  minimumStock: 10.0,
  maximumStock: 100.0,
  supplierId: "sup1",
  specifications: {
    Origin: "Mpumalanga, South Africa",
    Density: "2.7 g/cm³",
    "Compressive Strength": "200 MPa",
  },
  storageLocation: "Warehouse A - Section 1",
  isActive: true,
  lastRestocked: new Date("2024-01-15"),
  createdAt: new Date("2023-06-01"),
  updatedAt: new Date("2024-01-15"),
};

// Mock stock transactions
const mockTransactions: StockTransaction[] = [
  {
    id: "st1",
    materialId: "rm1",
    merchantId: "m1",
    type: "receive",
    quantity: 15.0,
    unitCost: convertToZAR(450.00),
    totalCost: convertToZAR(6750.00),
    balanceAfter: 25.5,
    reference: "PO-2024-001",
    notes: "Delivery from Cape Granite Suppliers",
    supplierId: "sup1",
    orderId: "po1",
    createdBy: "admin",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "st2",
    materialId: "rm1",
    merchantId: "m1",
    type: "usage",
    quantity: -5.0,
    balanceAfter: 20.5,
    reference: "JOB-2024-003",
    notes: "Used for Memorial Stone production",
    usageReason: "manufacturing",
    createdBy: "production_manager",
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-12"),
  },
  {
    id: "st3",
    materialId: "rm1",
    merchantId: "m1",
    type: "usage",
    quantity: -2.5,
    balanceAfter: 18.0,
    reference: "JOB-2024-002",
    notes: "Used for Custom Memorial production",
    usageReason: "manufacturing",
    createdBy: "production_manager",
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-08"),
  },
  {
    id: "st4",
    materialId: "rm1",
    merchantId: "m1",
    type: "order",
    quantity: 20.0,
    unitCost: convertToZAR(450.00),
    totalCost: convertToZAR(9000.00),
    balanceAfter: 18.0,
    reference: "PO-2024-001",
    notes: "Purchase order sent to Cape Granite Suppliers",
    supplierId: "sup1",
    orderId: "po1",
    createdBy: "admin",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-05"),
  },
];

// Mock purchase orders
const mockPurchaseOrders: PurchaseOrder[] = [
  {
    id: "po1",
    merchantId: "m1",
    supplierId: "sup1",
    orderNumber: "PO-2024-001",
    status: "completed",
    orderDate: new Date("2024-01-05"),
    expectedDeliveryDate: new Date("2024-01-12"),
    actualDeliveryDate: new Date("2024-01-15"),
    items: [
      {
        id: "poi1",
        materialId: "rm1",
        quantity: 20.0,
        unitCost: convertToZAR(450.00),
        totalCost: convertToZAR(9000.00),
        receivedQuantity: 15.0,
        notes: "Partial delivery - 5m² back-ordered",
      },
    ],
    subtotal: convertToZAR(9000.00),
    tax: convertToZAR(1350.00),
    total: convertToZAR(10350.00),
    notes: "Urgent order for upcoming projects",
    createdBy: "admin",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-15"),
  },
];

export default function MaterialStockManagement() {
  const params = useParams();
  const materialId = params.id as string;
  const [material] = useState<RawMaterial>(mockMaterial);

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "order":
        return ShoppingCart;
      case "receive":
        return Truck;
      case "usage":
        return Wrench;
      case "adjustment":
        return FileText;
      case "return":
        return TrendingUp;
      default:
        return Package;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case "order":
        return "bg-blue-100 text-blue-800";
      case "receive":
        return "bg-green-100 text-green-800";
      case "usage":
        return "bg-red-100 text-red-800";
      case "adjustment":
        return "bg-yellow-100 text-yellow-800";
      case "return":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "confirmed":
        return "bg-blue-100 text-blue-800";
      case "partially_received":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStockStatus = () => {
    if (material.currentStock <= material.minimumStock) {
      return { status: "Low Stock", color: "bg-red-100 text-red-800", icon: AlertTriangle };
    } else if (material.currentStock >= material.maximumStock * 0.8) {
      return { status: "High Stock", color: "bg-blue-100 text-blue-800", icon: CheckCircle };
    } else {
      return { status: "Normal", color: "bg-green-100 text-green-800", icon: CheckCircle };
    }
  };

  const stockStatus = getStockStatus();
  const StockIcon = stockStatus.icon;

  const totalOrdered = mockTransactions
    .filter(t => t.type === "order")
    .reduce((sum, t) => sum + t.quantity, 0);
  
  const totalReceived = mockTransactions
    .filter(t => t.type === "receive")
    .reduce((sum, t) => sum + t.quantity, 0);
  
  const totalUsed = mockTransactions
    .filter(t => t.type === "usage")
    .reduce((sum, t) => sum + Math.abs(t.quantity), 0);

  const stockValue = material.currentStock * material.costPerUnit;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title={`Stock Management: ${material.name}`}
        description="Track orders, receipts, and usage for this material"
        showBackButton
        actions={
          <div className="flex gap-2">
            <Link href={`/merchant/raw-materials/${materialId}/stock/order`}>
              <Button variant="outline">
                <ShoppingCart className="mr-2 h-4 w-4" />
                New Order
              </Button>
            </Link>
            <Link href={`/merchant/raw-materials/${materialId}/stock/receive`}>
              <Button variant="outline">
                <Truck className="mr-2 h-4 w-4" />
                Receive Stock
              </Button>
            </Link>
            <Link href={`/merchant/raw-materials/${materialId}/stock/usage`}>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Record Usage
              </Button>
            </Link>
          </div>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Stock</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {material.currentStock} {material.unit}
            </div>
            <div className="flex items-center gap-1 mt-1">
              <Badge className={stockStatus.color}>
                <StockIcon className="mr-1 h-3 w-3" />
                {stockStatus.status}
              </Badge>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatZAR(stockValue)}</div>
            <p className="text-xs text-muted-foreground">
              @ {formatZAR(material.costPerUnit)}/{material.unit}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Ordered</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalOrdered} {material.unit}
            </div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Received</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalReceived} {material.unit}
            </div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Used</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalUsed} {material.unit}
            </div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="transactions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="transactions">Transaction History</TabsTrigger>
          <TabsTrigger value="orders">Purchase Orders</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="transactions" className="space-y-4">
          <div className="space-y-4">
            {mockTransactions.map((transaction) => {
              const TransactionIcon = getTransactionIcon(transaction.type);
              
              return (
                <Card key={transaction.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-muted">
                          <TransactionIcon className="h-4 w-4" />
                        </div>
                        <div>
                          <CardTitle className="text-lg capitalize">
                            {transaction.type}
                          </CardTitle>
                          <CardDescription>
                            {transaction.createdAt.toLocaleDateString()} • {transaction.createdBy}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getTransactionColor(transaction.type)}>
                          {transaction.type}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-4">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Quantity</p>
                          <p className="text-sm text-muted-foreground">
                            {transaction.quantity > 0 ? '+' : ''}{transaction.quantity} {material.unit}
                          </p>
                        </div>
                      </div>
                      {transaction.unitCost && (
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">Unit Cost</p>
                            <p className="text-sm text-muted-foreground">
                              {formatZAR(transaction.unitCost)}
                            </p>
                          </div>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Balance After</p>
                          <p className="text-sm text-muted-foreground">
                            {transaction.balanceAfter} {material.unit}
                          </p>
                        </div>
                      </div>
                      {transaction.reference && (
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">Reference</p>
                            <p className="text-sm text-muted-foreground">
                              {transaction.reference}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                    {transaction.notes && (
                      <div className="mt-3 p-3 bg-muted rounded-md">
                        <p className="text-sm">{transaction.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <div className="space-y-4">
            {mockPurchaseOrders.map((order) => (
              <Card key={order.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
                      <CardDescription>
                        Ordered: {order.orderDate.toLocaleDateString()}
                        {order.actualDeliveryDate && (
                          <> • Delivered: {order.actualDeliveryDate.toLocaleDateString()}</>
                        )}
                      </CardDescription>
                    </div>
                    <Badge className={getOrderStatusColor(order.status)}>
                      {order.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-3 bg-muted rounded-md">
                        <div>
                          <p className="font-medium">{material.name}</p>
                          <p className="text-sm text-muted-foreground">
                            Ordered: {item.quantity} {material.unit} • 
                            Received: {item.receivedQuantity} {material.unit}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatZAR(item.totalCost)}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatZAR(item.unitCost)}/{material.unit}
                          </p>
                        </div>
                      </div>
                    ))}
                    <div className="flex items-center justify-between pt-3 border-t">
                      <span className="font-medium">Total Order Value:</span>
                      <span className="font-bold text-lg">{formatZAR(order.total)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Stock Levels</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Current Stock</span>
                  <span className="font-medium">{material.currentStock} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Minimum Stock</span>
                  <span className="font-medium">{material.minimumStock} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Maximum Stock</span>
                  <span className="font-medium">{material.maximumStock} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Stock Utilization</span>
                  <span className="font-medium">
                    {Math.round((material.currentStock / material.maximumStock) * 100)}%
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Usage Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Ordered</span>
                  <span className="font-medium">{totalOrdered} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Received</span>
                  <span className="font-medium">{totalReceived} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Used</span>
                  <span className="font-medium">{totalUsed} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Efficiency Rate</span>
                  <span className="font-medium">
                    {totalReceived > 0 ? Math.round((totalUsed / totalReceived) * 100) : 0}%
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
