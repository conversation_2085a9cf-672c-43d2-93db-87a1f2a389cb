"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Save,
  ArrowLeft,
  Package,
  ShoppingCart,
  DollarSign,
  FileText,
  Calendar,
  Building,
  AlertTriangle,
  TrendingUp,
  Clock,
} from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterial, RawMaterialSupplier } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock material data
const mockMaterial: RawMaterial = {
  id: "rm1",
  merchantId: "m1",
  name: "Premium Black Granite",
  description: "High-quality black granite from Mpumalanga quarries",
  category: "stone",
  unit: "m²",
  costPerUnit: convertToZAR(450.00),
  currentStock: 25.5,
  minimumStock: 10.0,
  maximumStock: 100.0,
  supplierId: "sup1",
  specifications: {},
  storageLocation: "Warehouse A - Section 1",
  isActive: true,
  lastRestocked: new Date("2024-01-15"),
  createdAt: new Date("2023-06-01"),
  updatedAt: new Date("2024-01-15"),
};

// Mock suppliers
const mockSuppliers: RawMaterialSupplier[] = [
  {
    id: "sup1",
    name: "Cape Granite Suppliers",
    contactPerson: "Johan van der Merwe",
    email: "<EMAIL>",
    phone: "+27 21 555 0123",
    address: "15 Industrial Road, Cape Town, 7405",
    rating: 4.8,
    isActive: true,
    paymentTerms: "30 days",
    deliveryTime: 5,
    createdAt: new Date("2023-01-15"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "sup2",
    name: "Johannesburg Stone Works",
    contactPerson: "Sipho Mthembu",
    email: "<EMAIL>",
    phone: "+27 11 555 0456",
    address: "42 Mining Street, Johannesburg, 2001",
    rating: 4.6,
    isActive: true,
    paymentTerms: "15 days",
    deliveryTime: 3,
    createdAt: new Date("2023-03-10"),
    updatedAt: new Date("2024-01-18"),
  },
];

const urgencyLevels = [
  { value: "normal", label: "Normal", description: "Standard delivery timeline" },
  { value: "urgent", label: "Urgent", description: "Rush delivery needed" },
  { value: "emergency", label: "Emergency", description: "Immediate delivery required" },
];

export default function NewOrder() {
  const params = useParams();
  const router = useRouter();
  const materialId = params.id as string;
  const [material] = useState<RawMaterial>(mockMaterial);

  const [formData, setFormData] = useState({
    quantity: "",
    unitCost: material.costPerUnit.toString(),
    supplierId: material.supplierId,
    expectedDeliveryDate: "",
    urgency: "normal",
    notes: "",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const quantity = parseFloat(formData.quantity);
    const unitCost = parseFloat(formData.unitCost);
    const totalCost = quantity * unitCost;
    const orderNumber = `PO-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`;

    // TODO: Implement actual order creation
    console.log("Creating purchase order:", {
      orderNumber,
      materialId,
      quantity,
      unitCost,
      totalCost,
      supplierId: formData.supplierId,
      expectedDeliveryDate: formData.expectedDeliveryDate ? new Date(formData.expectedDeliveryDate) : undefined,
      urgency: formData.urgency,
      notes: formData.notes,
      status: "draft",
    });

    router.push(`/merchant/raw-materials/${materialId}/stock`);
  };

  const handleCancel = () => {
    router.push(`/merchant/raw-materials/${materialId}/stock`);
  };

  const selectedSupplier = mockSuppliers.find(s => s.id === formData.supplierId);
  const quantity = parseFloat(formData.quantity || "0");
  const unitCost = parseFloat(formData.unitCost || "0");
  const totalCost = quantity * unitCost;
  const newStock = material.currentStock + quantity;
  const isLowStock = material.currentStock <= material.minimumStock;
  const suggestedQuantity = Math.max(material.maximumStock - material.currentStock, material.minimumStock * 2);

  // Calculate expected delivery date based on supplier delivery time
  const getExpectedDeliveryDate = () => {
    if (selectedSupplier) {
      const today = new Date();
      const expectedDate = new Date(today);
      expectedDate.setDate(today.getDate() + selectedSupplier.deliveryTime);
      return expectedDate.toISOString().split('T')[0];
    }
    return "";
  };

  // Auto-set expected delivery date when supplier changes
  const handleSupplierChange = (supplierId: string) => {
    handleInputChange("supplierId", supplierId);
    const supplier = mockSuppliers.find(s => s.id === supplierId);
    if (supplier && !formData.expectedDeliveryDate) {
      const today = new Date();
      const expectedDate = new Date(today);
      expectedDate.setDate(today.getDate() + supplier.deliveryTime);
      handleInputChange("expectedDeliveryDate", expectedDate.toISOString().split('T')[0]);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Create Purchase Order"
        description={`Order new stock for ${material.name}`}
        showBackButton
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={!formData.quantity || !formData.supplierId}>
              <Save className="mr-2 h-4 w-4" />
              Create Order
            </Button>
          </div>
        }
      />

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid gap-8 lg:grid-cols-3">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Material Info */}
            <Card>
              <CardHeader>
                <CardTitle>Material Information</CardTitle>
                <CardDescription>
                  Current stock levels and material details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label>Material Name</Label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="font-medium">{material.name}</p>
                      <p className="text-sm text-muted-foreground">{material.category}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Current Stock</Label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="font-medium text-lg">
                        {material.currentStock} {material.unit}
                      </p>
                      {isLowStock && (
                        <div className="flex items-center gap-1 mt-1">
                          <AlertTriangle className="h-3 w-3 text-red-600" />
                          <span className="text-xs text-red-600">Low stock</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Suggested Order</Label>
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <p className="font-medium text-blue-900">
                        {suggestedQuantity.toFixed(1)} {material.unit}
                      </p>
                      <p className="text-xs text-blue-700">To reach optimal level</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Details */}
            <Card>
              <CardHeader>
                <CardTitle>Order Details</CardTitle>
                <CardDescription>
                  Specify the quantity and supplier for this order
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">Quantity to Order *</Label>
                    <div className="relative">
                      <Package className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="quantity"
                        type="number"
                        step="0.01"
                        min="0.01"
                        placeholder={`${suggestedQuantity.toFixed(1)} ${material.unit}`}
                        value={formData.quantity}
                        onChange={(e) => handleInputChange("quantity", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Suggested: {suggestedQuantity.toFixed(1)} {material.unit}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unitCost">Expected Unit Cost (ZAR) *</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="unitCost"
                        type="number"
                        step="0.01"
                        min="0.01"
                        placeholder="0.00"
                        value={formData.unitCost}
                        onChange={(e) => handleInputChange("unitCost", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Last cost: {formatZAR(material.costPerUnit)}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="supplierId">Supplier *</Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground z-10" />
                    <Select
                      value={formData.supplierId}
                      onValueChange={handleSupplierChange}
                    >
                      <SelectTrigger className="pl-10">
                        <SelectValue placeholder="Select supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockSuppliers.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id}>
                            <div className="flex items-center justify-between w-full">
                              <span>{supplier.name}</span>
                              <span className="text-xs text-muted-foreground ml-2">
                                {supplier.deliveryTime} days
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="expectedDeliveryDate">Expected Delivery Date</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="expectedDeliveryDate"
                        type="date"
                        value={formData.expectedDeliveryDate}
                        onChange={(e) => handleInputChange("expectedDeliveryDate", e.target.value)}
                        className="pl-10"
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                    {selectedSupplier && (
                      <p className="text-xs text-muted-foreground">
                        Typical delivery: {selectedSupplier.deliveryTime} days
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="urgency">Order Urgency</Label>
                    <div className="relative">
                      <TrendingUp className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground z-10" />
                      <Select
                        value={formData.urgency}
                        onValueChange={(value) => handleInputChange("urgency", value)}
                      >
                        <SelectTrigger className="pl-10">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {urgencyLevels.map((level) => (
                            <SelectItem key={level.value} value={level.value}>
                              <div>
                                <div className="font-medium">{level.label}</div>
                                <div className="text-xs text-muted-foreground">{level.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Order Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Any special instructions, quality requirements, or delivery notes..."
                    value={formData.notes}
                    onChange={(e) => handleInputChange("notes", e.target.value)}
                    rows={3}
                  />
                </div>

                {selectedSupplier && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                    <h4 className="font-medium text-green-900 mb-2">Supplier Information</h4>
                    <div className="grid gap-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-green-700">Contact Person:</span>
                        <span className="font-medium">{selectedSupplier.contactPerson}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">Payment Terms:</span>
                        <span className="font-medium">{selectedSupplier.paymentTerms}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">Delivery Time:</span>
                        <span className="font-medium">{selectedSupplier.deliveryTime} days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">Rating:</span>
                        <span className="font-medium">{selectedSupplier.rating}/5.0</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
                <CardDescription>
                  Cost breakdown and stock impact
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Quantity</span>
                  <span className="font-medium">
                    {formData.quantity || "0"} {material.unit}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Unit Cost</span>
                  <span className="font-medium">{formatZAR(unitCost)}</span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Subtotal</span>
                    <span className="font-bold">{formatZAR(totalCost)}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">VAT (15%)</span>
                    <span>{formatZAR(totalCost * 0.15)}</span>
                  </div>
                  <div className="flex items-center justify-between border-t pt-2">
                    <span className="font-bold">Total</span>
                    <span className="font-bold text-lg">{formatZAR(totalCost * 1.15)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Stock Impact */}
            <Card>
              <CardHeader>
                <CardTitle>Stock Impact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Current Stock:</span>
                  <span className="font-medium">{material.currentStock} {material.unit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">After Delivery:</span>
                  <span className="font-medium text-green-600">
                    {newStock.toFixed(2)} {material.unit}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Minimum Level:</span>
                  <span className="font-medium">{material.minimumStock} {material.unit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Maximum Level:</span>
                  <span className="font-medium">{material.maximumStock} {material.unit}</span>
                </div>
              </CardContent>
            </Card>

            {/* Delivery Timeline */}
            {selectedSupplier && formData.expectedDeliveryDate && (
              <Card>
                <CardHeader>
                  <CardTitle>Delivery Timeline</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Expected Delivery</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(formData.expectedDeliveryDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Urgency Level</p>
                      <p className="text-sm text-muted-foreground capitalize">
                        {formData.urgency}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Guidelines */}
            <Card>
              <CardHeader>
                <CardTitle>Guidelines</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-muted-foreground">
                <p>• Consider current stock levels and usage patterns</p>
                <p>• Verify supplier pricing before ordering</p>
                <p>• Account for delivery times in planning</p>
                <p>• Include quality specifications in notes</p>
                <p>• Confirm storage capacity for large orders</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
