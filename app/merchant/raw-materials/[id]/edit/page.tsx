"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Save,
  ArrowLeft,
  Package2,
  DollarSign,
  Warehouse,
  Calendar,
  Plus,
  X,
} from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterial } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock material data
const mockMaterial: RawMaterial = {
  id: "rm1",
  merchantId: "m1",
  name: "Premium Black Granite",
  description: "High-quality black granite from Mpumalanga quarries",
  category: "stone",
  unit: "m²",
  costPerUnit: convertToZAR(450.00),
  currentStock: 25.5,
  minimumStock: 10.0,
  maximumStock: 100.0,
  supplierId: "sup1",
  specifications: {
    Origin: "Mpumalanga, South Africa",
    Density: "2.7 g/cm³",
    "Compressive Strength": "200 MPa",
    "Water Absorption": "< 0.4%",
    Finish: "Rough Cut",
  },
  storageLocation: "Warehouse A - Section 1",
  isActive: true,
  lastRestocked: new Date("2024-01-15"),
  expiryDate: new Date("2026-01-15"),
  createdAt: new Date("2023-06-01"),
  updatedAt: new Date("2024-01-15"),
};

// Mock suppliers
const mockSuppliers = [
  { id: "sup1", name: "Cape Granite Suppliers" },
  { id: "sup2", name: "Johannesburg Stone Works" },
  { id: "sup3", name: "Durban Industrial Supplies" },
];

const materialCategories = [
  { value: "stone", label: "Stone Materials" },
  { value: "tools", label: "Tools & Equipment" },
  { value: "consumables", label: "Consumables" },
  { value: "hardware", label: "Hardware" },
  { value: "chemicals", label: "Chemicals" },
];

const units = [
  { value: "m²", label: "Square Meters (m²)" },
  { value: "kg", label: "Kilograms (kg)" },
  { value: "pieces", label: "Pieces" },
  { value: "liters", label: "Liters" },
  { value: "tons", label: "Tons" },
  { value: "boxes", label: "Boxes" },
];

export default function EditRawMaterial() {
  const params = useParams();
  const router = useRouter();
  const materialId = params.id as string;
  const [material, setMaterial] = useState<RawMaterial>(mockMaterial);

  const [formData, setFormData] = useState({
    name: material.name,
    description: material.description,
    category: material.category,
    unit: material.unit,
    costPerUnit: (material.costPerUnit / 100).toString(), // Convert from cents
    minimumStock: material.minimumStock.toString(),
    maximumStock: material.maximumStock.toString(),
    supplierId: material.supplierId,
    storageLocation: material.storageLocation,
    isActive: material.isActive,
    expiryDate: material.expiryDate?.toISOString().split('T')[0] || "",
    specifications: { ...material.specifications },
  });

  const [newSpecKey, setNewSpecKey] = useState("");
  const [newSpecValue, setNewSpecValue] = useState("");

  const handleInputChange = (field: string, value: string | boolean | number) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddSpecification = () => {
    if (newSpecKey && newSpecValue) {
      setFormData((prev) => ({
        ...prev,
        specifications: {
          ...prev.specifications,
          [newSpecKey]: newSpecValue,
        },
      }));
      setNewSpecKey("");
      setNewSpecValue("");
    }
  };

  const handleRemoveSpecification = (key: string) => {
    setFormData((prev) => {
      const newSpecs = { ...prev.specifications };
      delete newSpecs[key];
      return { ...prev, specifications: newSpecs };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // TODO: Implement actual material update
    const updatedMaterial: RawMaterial = {
      ...material,
      name: formData.name,
      description: formData.description,
      category: formData.category,
      unit: formData.unit,
      costPerUnit: convertToZAR(parseFloat(formData.costPerUnit)),
      minimumStock: parseFloat(formData.minimumStock),
      maximumStock: parseFloat(formData.maximumStock),
      supplierId: formData.supplierId,
      storageLocation: formData.storageLocation,
      isActive: formData.isActive,
      expiryDate: formData.expiryDate ? new Date(formData.expiryDate) : undefined,
      specifications: formData.specifications,
      updatedAt: new Date(),
    };
    
    console.log("Updating material:", updatedMaterial);
    setMaterial(updatedMaterial);
    router.push(`/merchant/raw-materials/${materialId}`);
  };

  const handleCancel = () => {
    router.push(`/merchant/raw-materials/${materialId}`);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Edit Raw Material"
        description={`Update ${material.name} information`}
        showBackButton
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        }
      />

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid gap-8 lg:grid-cols-3">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Update the basic details for this raw material
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Material Name *</Label>
                  <Input
                    id="name"
                    placeholder="e.g., Premium Black Granite"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe the raw material, its properties, and uses..."
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    rows={3}
                    required
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => handleInputChange("category", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {materialCategories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">Unit of Measurement *</Label>
                    <Select
                      value={formData.unit}
                      onValueChange={(value) => handleInputChange("unit", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        {units.map((unit) => (
                          <SelectItem key={unit.value} value={unit.value}>
                            {unit.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="costPerUnit">Cost per Unit (ZAR) *</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="costPerUnit"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      value={formData.costPerUnit}
                      onChange={(e) => handleInputChange("costPerUnit", e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Stock Management */}
            <Card>
              <CardHeader>
                <CardTitle>Stock Management</CardTitle>
                <CardDescription>
                  Configure stock levels and storage information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="minimumStock">Minimum Stock *</Label>
                    <div className="relative">
                      <Package2 className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="minimumStock"
                        type="number"
                        step="0.01"
                        placeholder="0"
                        value={formData.minimumStock}
                        onChange={(e) => handleInputChange("minimumStock", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maximumStock">Maximum Stock *</Label>
                    <div className="relative">
                      <Package2 className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="maximumStock"
                        type="number"
                        step="0.01"
                        placeholder="0"
                        value={formData.maximumStock}
                        onChange={(e) => handleInputChange("maximumStock", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="storageLocation">Storage Location *</Label>
                    <div className="relative">
                      <Warehouse className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="storageLocation"
                        placeholder="e.g., Warehouse A - Section 1"
                        value={formData.storageLocation}
                        onChange={(e) => handleInputChange("storageLocation", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="expiryDate">Expiry Date (Optional)</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="expiryDate"
                        type="date"
                        value={formData.expiryDate}
                        onChange={(e) => handleInputChange("expiryDate", e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="supplierId">Primary Supplier *</Label>
                  <Select
                    value={formData.supplierId}
                    onValueChange={(value) => handleInputChange("supplierId", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockSuppliers.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.id}>
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Specifications */}
            <Card>
              <CardHeader>
                <CardTitle>Specifications</CardTitle>
                <CardDescription>
                  Update detailed specifications for this material
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Specification name"
                    value={newSpecKey}
                    onChange={(e) => setNewSpecKey(e.target.value)}
                  />
                  <Input
                    placeholder="Specification value"
                    value={newSpecValue}
                    onChange={(e) => setNewSpecValue(e.target.value)}
                  />
                  <Button
                    type="button"
                    onClick={handleAddSpecification}
                    disabled={!newSpecKey || !newSpecValue}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                {Object.entries(formData.specifications).length > 0 && (
                  <div className="space-y-2">
                    {Object.entries(formData.specifications).map(([key, value]) => (
                      <div
                        key={key}
                        className="flex items-center justify-between p-2 bg-muted rounded-md"
                      >
                        <span className="text-sm">
                          <strong>{key}:</strong> {value}
                        </span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveSpecification(key)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Material Status</CardTitle>
                <CardDescription>
                  Control material availability
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="isActive">Active Material</Label>
                    <p className="text-xs text-muted-foreground">
                      Enable this material for use
                    </p>
                  </div>
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange("isActive", checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Current Stock Info */}
            <Card>
              <CardHeader>
                <CardTitle>Current Stock</CardTitle>
                <CardDescription>
                  Current inventory levels (read-only)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Current Stock:</span>
                  <span className="font-medium">{material.currentStock} {material.unit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Stock Value:</span>
                  <span className="font-medium">
                    {formatZAR(material.currentStock * material.costPerUnit)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Last Restocked:</span>
                  <span className="font-medium">
                    {material.lastRestocked.toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Category:</span>
                  <span className="font-medium">{formData.category}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Unit:</span>
                  <span className="font-medium">{formData.unit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Cost per Unit:</span>
                  <span className="font-medium">{formatZAR(parseFloat(formData.costPerUnit || "0") * 100)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Min Stock:</span>
                  <span className="font-medium">{formData.minimumStock} {formData.unit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Max Stock:</span>
                  <span className="font-medium">{formData.maximumStock} {formData.unit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  <span className={`font-medium ${formData.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                    {formData.isActive ? "Active" : "Inactive"}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
