"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Edit,
  Package2,
  AlertTriangle,
  CheckCircle,
  Warehouse,
  Calendar,
  DollarSign,
  TrendingUp,
  Building,
  Star,
  Phone,
  Mail,
} from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterial, RawMaterialSupplier, StockTransaction } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock material data
const mockMaterial: RawMaterial = {
  id: "rm1",
  merchantId: "m1",
  name: "Premium Black Granite",
  description: "High-quality black granite from Mpumalanga quarries, perfect for memorial stones and monuments. This premium grade granite offers exceptional durability and a beautiful deep black finish.",
  category: "stone",
  unit: "m²",
  costPerUnit: convertToZAR(450.00),
  currentStock: 25.5,
  minimumStock: 10.0,
  maximumStock: 100.0,
  supplierId: "sup1",
  specifications: {
    Origin: "Mpumalanga, South Africa",
    Density: "2.7 g/cm³",
    "Compressive Strength": "200 MPa",
    "Water Absorption": "< 0.4%",
    Finish: "Rough Cut",
    "Frost Resistance": "Excellent",
    "Chemical Resistance": "High",
  },
  storageLocation: "Warehouse A - Section 1",
  isActive: true,
  lastRestocked: new Date("2024-01-15"),
  expiryDate: new Date("2026-01-15"),
  createdAt: new Date("2023-06-01"),
  updatedAt: new Date("2024-01-15"),
};

// Mock supplier data
const mockSupplier: RawMaterialSupplier = {
  id: "sup1",
  name: "Cape Granite Suppliers",
  contactPerson: "Johan van der Merwe",
  email: "<EMAIL>",
  phone: "+27 21 555 0123",
  address: "15 Industrial Road, Cape Town, 7405",
  rating: 4.8,
  isActive: true,
  paymentTerms: "30 days",
  deliveryTime: 5,
  createdAt: new Date("2023-01-15"),
  updatedAt: new Date("2024-01-20"),
};

// Mock recent transactions
const mockRecentTransactions: StockTransaction[] = [
  {
    id: "st1",
    materialId: "rm1",
    merchantId: "m1",
    type: "receive",
    quantity: 15.0,
    unitCost: convertToZAR(450.00),
    totalCost: convertToZAR(6750.00),
    balanceAfter: 25.5,
    reference: "PO-2024-001",
    notes: "Delivery from Cape Granite Suppliers",
    supplierId: "sup1",
    createdBy: "admin",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "st2",
    materialId: "rm1",
    merchantId: "m1",
    type: "usage",
    quantity: -5.0,
    balanceAfter: 20.5,
    reference: "JOB-2024-003",
    notes: "Used for Memorial Stone production",
    usageReason: "manufacturing",
    createdBy: "production_manager",
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-12"),
  },
];

export default function RawMaterialDetails() {
  const params = useParams();
  const materialId = params.id as string;
  const [material] = useState<RawMaterial>(mockMaterial);
  const [supplier] = useState<RawMaterialSupplier>(mockSupplier);

  const getStockStatus = () => {
    if (material.currentStock <= material.minimumStock) {
      return { status: "Low Stock", color: "bg-red-100 text-red-800", icon: AlertTriangle };
    } else if (material.currentStock >= material.maximumStock * 0.8) {
      return { status: "High Stock", color: "bg-blue-100 text-blue-800", icon: CheckCircle };
    } else {
      return { status: "Normal", color: "bg-green-100 text-green-800", icon: CheckCircle };
    }
  };

  const stockStatus = getStockStatus();
  const StockIcon = stockStatus.icon;
  const stockValue = material.currentStock * material.costPerUnit;
  const stockUtilization = Math.round((material.currentStock / material.maximumStock) * 100);

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "receive":
        return "📦";
      case "usage":
        return "🔧";
      case "order":
        return "🛒";
      default:
        return "📋";
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "fill-yellow-400 text-yellow-400"
            : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title={material.name}
        description="Raw material details and inventory information"
        showBackButton
        actions={
          <div className="flex gap-2">
            <Link href={`/merchant/raw-materials/${materialId}/stock`}>
              <Button variant="outline">
                <Package2 className="mr-2 h-4 w-4" />
                Manage Stock
              </Button>
            </Link>
            <Link href={`/merchant/raw-materials/${materialId}/edit`}>
              <Button>
                <Edit className="mr-2 h-4 w-4" />
                Edit Material
              </Button>
            </Link>
          </div>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Stock</CardTitle>
            <Package2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {material.currentStock} {material.unit}
            </div>
            <div className="flex items-center gap-1 mt-1">
              <Badge className={stockStatus.color}>
                <StockIcon className="mr-1 h-3 w-3" />
                {stockStatus.status}
              </Badge>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatZAR(stockValue)}</div>
            <p className="text-xs text-muted-foreground">
              @ {formatZAR(material.costPerUnit)}/{material.unit}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Utilization</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stockUtilization}%</div>
            <p className="text-xs text-muted-foreground">
              of maximum capacity
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Restocked</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.floor((Date.now() - material.lastRestocked.getTime()) / (1000 * 60 * 60 * 24))}
            </div>
            <p className="text-xs text-muted-foreground">days ago</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="specifications">Specifications</TabsTrigger>
          <TabsTrigger value="supplier">Supplier</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Description</Label>
                  <p className="text-sm text-muted-foreground">{material.description}</p>
                </div>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label className="text-sm font-medium">Category</Label>
                    <p className="text-sm">{material.category}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Unit</Label>
                    <p className="text-sm">{material.unit}</p>
                  </div>
                </div>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label className="text-sm font-medium">Cost per Unit</Label>
                    <p className="text-sm font-semibold">{formatZAR(material.costPerUnit)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Status</Label>
                    <Badge variant={material.isActive ? "default" : "secondary"}>
                      {material.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Stock Levels */}
            <Card>
              <CardHeader>
                <CardTitle>Stock Levels</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Current Stock</span>
                  <span className="font-medium">{material.currentStock} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Minimum Stock</span>
                  <span className="font-medium">{material.minimumStock} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Maximum Stock</span>
                  <span className="font-medium">{material.maximumStock} {material.unit}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Storage Location</span>
                  <div className="flex items-center gap-1">
                    <Warehouse className="h-3 w-3" />
                    <span className="font-medium">{material.storageLocation}</span>
                  </div>
                </div>
                {material.expiryDate && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Expiry Date</span>
                    <span className="font-medium">{material.expiryDate.toLocaleDateString()}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="specifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Technical Specifications</CardTitle>
              <CardDescription>
                Detailed technical properties and characteristics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {Object.entries(material.specifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-muted rounded-md">
                    <span className="font-medium">{key}</span>
                    <span className="text-muted-foreground">{value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="supplier" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Supplier Information</CardTitle>
              <CardDescription>
                Details about the primary supplier for this material
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-muted rounded-lg">
                  <Building className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">{supplier.name}</h3>
                  <p className="text-muted-foreground">Contact: {supplier.contactPerson}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <div className="flex items-center">
                      {renderStars(supplier.rating)}
                    </div>
                    <span className="text-sm font-medium">{supplier.rating}/5.0</span>
                  </div>
                </div>
                <Link href={`/merchant/raw-materials/suppliers/${supplier.id}`}>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </Link>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{supplier.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{supplier.phone}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Payment Terms</span>
                    <span className="text-sm font-medium">{supplier.paymentTerms}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Delivery Time</span>
                    <span className="text-sm font-medium">{supplier.deliveryTime} days</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <div className="space-y-4">
            {mockRecentTransactions.map((transaction) => (
              <Card key={transaction.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{getTransactionIcon(transaction.type)}</span>
                      <div>
                        <CardTitle className="text-lg capitalize">
                          {transaction.type}
                        </CardTitle>
                        <CardDescription>
                          {transaction.createdAt.toLocaleDateString()} • {transaction.createdBy}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">
                        {transaction.quantity > 0 ? '+' : ''}{transaction.quantity} {material.unit}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Balance: {transaction.balanceAfter} {material.unit}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                {transaction.notes && (
                  <CardContent>
                    <p className="text-sm text-muted-foreground">{transaction.notes}</p>
                    {transaction.reference && (
                      <p className="text-sm font-medium mt-1">Ref: {transaction.reference}</p>
                    )}
                  </CardContent>
                )}
              </Card>
            ))}
            <div className="text-center">
              <Link href={`/merchant/raw-materials/${materialId}/stock`}>
                <Button variant="outline">
                  View All Transactions
                </Button>
              </Link>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function Label({ className, children, ...props }: { className?: string; children: React.ReactNode }) {
  return (
    <label className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className || ''}`} {...props}>
      {children}
    </label>
  );
}
