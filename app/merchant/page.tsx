"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  Package,
  Wrench,
  Calendar,
  TrendingUp,
  AlertCircle,
  Clock,
  Star,
  Eye,
  BarChart3,
  DollarSign,
  Factory,
  PieChart,
  LineChart,
  Target,
  Truck,
  Activity,
  Zap,
  ArrowUpRight,
  ArrowDownRight,
  TrendingDown,
} from "lucide-react";
import {
  MerchantDashboardStats,
  GroupPurchase,
  InstallationTask,
} from "@/types/merchant";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import { formatZAR } from "@/data/south-african-context";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data - replace with actual API calls
const mockStats: MerchantDashboardStats = {
  totalGroups: 24,
  activeGroups: 18,
  readyForManufacturing: 5,
  inManufacturing: 8,
  pendingInstallation: 3,
  completedInstallations: 12,
  totalRevenue: 1875000, // R1.875M
  monthlyRevenue: 277500, // R277.5K
  averageGroupSize: 6.2,
  averageOrderValue: 42750, // R42.75K
};

// Enhanced mock data with real product images and details
type DashboardGroupPurchase = Omit<GroupPurchase, "productImage"> & {
  productName: string;
  productImage: string;
  category: string;
  estimatedCompletion?: Date;
};

const mockRecentGroups: DashboardGroupPurchase[] = [
  {
    id: "g1",
    productId: "p1",
    merchantId: "m1",
    groupName: "Memorial for John Smith",
    adminId: "u1",
    totalMembers: 8,
    targetAmount: 48000, // R48K
    currentAmount: 43200, // R43.2K
    paymentProgress: 90,
    status: "collecting",
    createdAt: new Date("2024-01-15"),
    productName: "Classic Granite Memorial Stone",
    productImage: getStoneImagesForCategory("traditional", 1)[0] || "",
    category: "traditional",
  },
  {
    id: "g2",
    productId: "p2",
    merchantId: "m1",
    groupName: "Family Heritage Stone",
    adminId: "u2",
    totalMembers: 12,
    targetAmount: 67500, // R67.5K
    currentAmount: 54000, // R54K
    paymentProgress: 80,
    status: "manufacturing",
    createdAt: new Date("2024-01-10"),
    manufacturingStarted: new Date("2024-01-20"),
    productName: "Modern Minimalist Memorial",
    productImage: getStoneImagesForCategory("modern", 1)[0] || "",
    category: "modern",
    estimatedCompletion: new Date("2024-02-20"),
  },
  {
    id: "g3",
    productId: "p3",
    merchantId: "m1",
    groupName: "Custom Engraved Memorial",
    adminId: "u3",
    totalMembers: 6,
    targetAmount: 78000, // R78K
    currentAmount: 70200, // R70.2K
    paymentProgress: 90,
    status: "manufacturing",
    createdAt: new Date("2024-01-08"),
    manufacturingStarted: new Date("2024-01-25"),
    productName: "Custom Engraved Memorial Stone",
    productImage: getStoneImagesForCategory("custom", 1)[0] || "",
    category: "custom",
    estimatedCompletion: new Date("2024-02-25"),
  },
  {
    id: "g4",
    productId: "p4",
    merchantId: "m1",
    groupName: "Premium Granite Monument",
    adminId: "u4",
    totalMembers: 15,
    targetAmount: 127500, // R127.5K
    currentAmount: 114750, // R114.75K
    paymentProgress: 90,
    status: "installing",
    createdAt: new Date("2024-01-05"),
    manufacturingStarted: new Date("2024-01-15"),
    manufacturingCompleted: new Date("2024-02-05"),
    productName: "Premium Granite Monument",
    productImage: getStoneImagesForCategory("premium", 1)[0] || "",
    category: "premium",
  },
];

type DashboardInstallationTask = InstallationTask & {
  productName: string;
  productImage: string;
  category: string;
  groupName: string;
};

const mockUpcomingInstallations: DashboardInstallationTask[] = [
  {
    id: "i1",
    groupId: "g3",
    productId: "p1",
    merchantId: "m1",
    title: "Memorial Installation - Westpark Cemetery",
    description: "Installation of granite memorial stone",
    installationAddress: "Westpark Cemetery, Johannesburg, Section B, Plot 45",
    scheduledDate: new Date("2024-02-15"),
    estimatedDuration: 4,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
    productName: "Classic Granite Memorial Stone",
    productImage: getStoneImagesForCategory("traditional", 1)[0] || "",
    category: "traditional",
    groupName: "Memorial for John Smith",
  },
  {
    id: "i2",
    groupId: "g5",
    productId: "p2",
    merchantId: "m1",
    title: "Modern Memorial Installation - Braamfontein Cemetery",
    description: "Installation of modern minimalist memorial stone",
    installationAddress:
      "Braamfontein Cemetery, Johannesburg, Garden Section, Plot 12",
    scheduledDate: new Date("2024-02-18"),
    estimatedDuration: 3,
    assignedTeam: ["t1", "t3"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-28"),
    updatedAt: new Date("2024-01-28"),
    productName: "Modern Minimalist Memorial",
    productImage: getStoneImagesForCategory("modern", 1)[0] || "",
    category: "modern",
    groupName: "Thompson Family Memorial",
  },
];

export default function MerchantDashboard() {
  const [stats] = useState<MerchantDashboardStats>(mockStats);
  const [recentGroups] = useState<DashboardGroupPurchase[]>(mockRecentGroups);
  const [upcomingInstallations] = useState<DashboardInstallationTask[]>(
    mockUpcomingInstallations
  );

  const getStatusColor = (status: GroupPurchase["status"]) => {
    switch (status) {
      case "discussing":
        return "bg-blue-100 text-blue-800";
      case "collecting":
        return "bg-yellow-100 text-yellow-800";
      case "manufacturing":
        return "bg-purple-100 text-purple-800";
      case "installing":
        return "bg-orange-100 text-orange-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Dashboard"
        description="Overview of your merchant operations and performance"
      />

      {/* Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="pipeline">Order Pipeline</TabsTrigger>
          <TabsTrigger value="materials">Materials & Pricing</TabsTrigger>
          <TabsTrigger value="production">Production Planning</TabsTrigger>
          <TabsTrigger value="financial">Financial Analytics</TabsTrigger>
          <TabsTrigger value="capacity">Capacity Planning</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-8">
          {/* Stats Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Groups
                </CardTitle>
                <Users className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalGroups}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.activeGroups} active groups
                </p>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-orange-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Ready for Manufacturing
                </CardTitle>
                <AlertCircle className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.readyForManufacturing}
                </div>
                <p className="text-xs text-muted-foreground">
                  80%+ payment collected
                </p>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-purple-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  In Manufacturing
                </CardTitle>
                <Wrench className="h-4 w-4 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.inManufacturing}
                </div>
                <p className="text-xs text-muted-foreground">
                  Currently in production
                </p>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Monthly Revenue
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatZAR(stats.monthlyRevenue)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {formatZAR(stats.totalRevenue)} total
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            {/* Recent Groups */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Groups</CardTitle>
                <CardDescription>
                  Latest group purchases and their payment progress
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentGroups.map((group) => (
                  <div
                    key={group.id}
                    className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                  >
                    {/* Product Image */}
                    <div className="relative w-16 h-16 flex-shrink-0 overflow-hidden rounded-lg bg-muted">
                      <Image
                        src={group.productImage || "/placeholder-image.jpg"}
                        alt={group.productName || "Product image"}
                        fill
                        className="object-cover"
                      />
                    </div>

                    {/* Group Details */}
                    <div className="flex-1 space-y-2">
                      <div className="flex items-start justify-between">
                        <div>
                          <p className="font-medium">{group.groupName}</p>
                          <p className="text-sm text-muted-foreground">
                            {group.productName}
                          </p>
                        </div>
                        <Link href={`/merchant/groups/${group.id}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="mr-1 h-3 w-3" />
                            View
                          </Button>
                        </Link>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(group.status)}>
                          {group.status}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {group.totalMembers} members
                        </span>
                        {group.status === "manufacturing" &&
                          group.estimatedCompletion && (
                            <span className="text-sm text-muted-foreground">
                              • Est.{" "}
                              {group.estimatedCompletion.toLocaleDateString(
                                "en-ZA"
                              )}
                            </span>
                          )}
                      </div>

                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="font-medium">
                            {formatZAR(group.currentAmount)}
                          </span>
                          <span className="text-muted-foreground">
                            {formatZAR(group.targetAmount)}
                          </span>
                        </div>
                        <Progress
                          value={group.paymentProgress}
                          className="h-2"
                        />
                        <div className="text-xs text-muted-foreground">
                          {group.paymentProgress}% collected
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                <Link href="/merchant/groups">
                  <Button variant="outline" className="w-full">
                    View All Groups
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Upcoming Installations */}
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Installations</CardTitle>
                <CardDescription>
                  Scheduled installation tasks and assignments
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {upcomingInstallations.map((installation) => (
                  <div
                    key={installation.id}
                    className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                  >
                    {/* Product Image */}
                    <div className="relative w-16 h-16 flex-shrink-0 overflow-hidden rounded-lg bg-muted">
                      <Image
                        src={
                          installation.productImage || "/placeholder-image.jpg"
                        }
                        alt={installation.productName || "Product image"}
                        fill
                        className="object-cover"
                      />
                    </div>

                    {/* Installation Details */}
                    <div className="flex-1 space-y-2">
                      <div className="flex items-start justify-between">
                        <div>
                          <p className="font-medium">{installation.title}</p>
                          <p className="text-sm text-muted-foreground">
                            {installation.groupName}
                          </p>
                        </div>
                        <Link
                          href={`/merchant/installations/${installation.id}`}
                        >
                          <Button variant="outline" size="sm">
                            <Eye className="mr-1 h-3 w-3" />
                            View
                          </Button>
                        </Link>
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>
                            {installation.scheduledDate.toLocaleDateString(
                              "en-ZA"
                            )}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{installation.estimatedDuration} hours</span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Badge
                          variant="outline"
                          className="text-blue-600 border-blue-600"
                        >
                          {installation.status}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {installation.productName}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
                <Link href="/merchant/installations">
                  <Button variant="outline" className="w-full">
                    View All Installations
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* Featured Products Showcase */}
          <Card>
            <CardHeader>
              <CardTitle>Featured Products</CardTitle>
              <CardDescription>
                Your most popular memorial stones across all categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {[
                  {
                    category: "traditional",
                    name: "Classic Granite Memorial",
                    price: 19485, // R19,485
                  },
                  {
                    category: "modern",
                    name: "Modern Minimalist Memorial",
                    price: 32985, // R32,985
                  },
                  {
                    category: "custom",
                    name: "Custom Engraved Memorial",
                    price: 52485, // R52,485
                  },
                  {
                    category: "premium",
                    name: "Premium Granite Monument",
                    price: 89985, // R89,985
                  },
                ].map((product, index) => {
                  const image = getStoneImagesForCategory(
                    product.category,
                    1
                  )[0];
                  return (
                    <div
                      key={index}
                      className="group relative overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all"
                    >
                      <div className="relative aspect-square overflow-hidden">
                        <Image
                          src={image}
                          alt={product.name}
                          fill
                          className="object-cover transition-transform group-hover:scale-105"
                        />
                        <div className="absolute top-2 right-2">
                          <Badge className="bg-yellow-500 text-yellow-900">
                            <Star className="w-3 h-3 mr-1 fill-current" />
                            Featured
                          </Badge>
                        </div>
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium mb-1">{product.name}</h3>
                        <p className="text-sm text-muted-foreground mb-2 capitalize">
                          {product.category} Collection
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="font-semibold">
                            {formatZAR(product.price)}
                          </span>
                          <Link href="/merchant/catalog">
                            <Button variant="outline" size="sm">
                              Manage
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Link href="/merchant/inventory/new">
                  <Button className="w-full" variant="outline">
                    <Package className="mr-2 h-4 w-4" />
                    Add New Product
                  </Button>
                </Link>
                <Link href="/merchant/installations/new">
                  <Button className="w-full" variant="outline">
                    <Calendar className="mr-2 h-4 w-4" />
                    Schedule Installation
                  </Button>
                </Link>
                <Link href="/merchant/team">
                  <Button className="w-full" variant="outline">
                    <Users className="mr-2 h-4 w-4" />
                    Manage Team
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Order Pipeline Tab */}
        <TabsContent value="pipeline" className="space-y-6">
          {/* Key Metrics Row */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card className="bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-600 text-sm font-medium">
                      Total Pipeline
                    </p>
                    <p className="text-3xl font-bold text-slate-900">50</p>
                    <p className="text-slate-500 text-xs">Active Orders</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Activity className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border border-emerald-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-emerald-700 text-sm font-medium">
                      Pipeline Value
                    </p>
                    <p className="text-3xl font-bold text-emerald-900">
                      {formatZAR(2400000).replace("R", "R")}
                    </p>
                    <p className="text-emerald-600 text-xs flex items-center">
                      <ArrowUpRight className="h-3 w-3 mr-1" />
                      +12% vs last month
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-emerald-100 rounded-full flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-emerald-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border border-amber-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-amber-700 text-sm font-medium">
                      Ready to Manufacture
                    </p>
                    <p className="text-3xl font-bold text-amber-900">6</p>
                    <p className="text-amber-600 text-xs">
                      100% Payment Complete
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center">
                    <Zap className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 border border-indigo-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-indigo-700 text-sm font-medium">
                      Avg. Completion
                    </p>
                    <p className="text-3xl font-bold text-indigo-900">68%</p>
                    <p className="text-indigo-600 text-xs flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      +5% this week
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center">
                    <Target className="h-6 w-6 text-indigo-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 lg:grid-cols-3">
            {/* Pipeline Funnel Visualization */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Payment Completion Funnel</span>
                </CardTitle>
                <CardDescription>
                  Interactive pipeline visualization - click stages for details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    {
                      stage: "0-25%",
                      count: 8,
                      amount: 384000,
                      width: "w-full",
                      color: "bg-rose-400",
                      bgColor: "bg-rose-50",
                      borderColor: "border-rose-200",
                    },
                    {
                      stage: "26-50%",
                      count: 12,
                      amount: 576000,
                      width: "w-5/6",
                      color: "bg-amber-400",
                      bgColor: "bg-amber-50",
                      borderColor: "border-amber-200",
                    },
                    {
                      stage: "51-75%",
                      count: 15,
                      amount: 720000,
                      width: "w-4/6",
                      color: "bg-yellow-400",
                      bgColor: "bg-yellow-50",
                      borderColor: "border-yellow-200",
                    },
                    {
                      stage: "76-99%",
                      count: 9,
                      amount: 432000,
                      width: "w-3/6",
                      color: "bg-blue-400",
                      bgColor: "bg-blue-50",
                      borderColor: "border-blue-200",
                    },
                    {
                      stage: "100%",
                      count: 6,
                      amount: 288000,
                      width: "w-2/6",
                      color: "bg-emerald-400",
                      bgColor: "bg-emerald-50",
                      borderColor: "border-emerald-200",
                    },
                  ].map((stage, index) => (
                    <div key={stage.stage} className="relative">
                      <div
                        className={`${stage.bgColor} ${stage.borderColor} border-2 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-[1.02]`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <div
                              className={`w-3 h-3 rounded-full ${stage.color}`}
                            />
                            <span className="font-semibold text-gray-800">
                              {stage.stage} Payment Complete
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Funnel Bar */}
                        <div className="mb-3">
                          <div className="bg-gray-200 rounded-full h-2 mb-2">
                            <div
                              className={`${stage.color} h-2 rounded-full ${stage.width} transition-all duration-1000`}
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Orders</p>
                            <p className="font-bold text-xl">{stage.count}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Value</p>
                            <p className="font-bold text-xl">
                              {formatZAR(stage.amount)}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">Avg. Order</p>
                            <p className="font-bold text-xl">
                              {formatZAR(stage.amount / stage.count)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Manufacturing Timeline Sidebar */}
            <Card className="bg-gradient-to-b from-gray-50 to-white">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <span>Manufacturing Timeline</span>
                </CardTitle>
                <CardDescription>
                  Projected schedule based on payment forecasts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      period: "This Week",
                      orders: 3,
                      granite: "Black Granite",
                      amount: 144000,
                      status: "confirmed",
                      completion: "Feb 5-9",
                      confidence: 95,
                      icon: Zap,
                      color: "text-emerald-600",
                      bgColor: "bg-emerald-100",
                    },
                    {
                      period: "Next Week",
                      orders: 5,
                      granite: "Grey Granite",
                      amount: 240000,
                      status: "likely",
                      completion: "Feb 12-16",
                      confidence: 85,
                      icon: TrendingUp,
                      color: "text-blue-600",
                      bgColor: "bg-blue-100",
                    },
                    {
                      period: "Week 3",
                      orders: 4,
                      granite: "Mixed Types",
                      amount: 192000,
                      status: "projected",
                      completion: "Feb 19-23",
                      confidence: 70,
                      icon: Target,
                      color: "text-amber-600",
                      bgColor: "bg-amber-100",
                    },
                    {
                      period: "Week 4",
                      orders: 6,
                      granite: "Premium Black",
                      amount: 288000,
                      status: "projected",
                      completion: "Feb 26-Mar 2",
                      confidence: 65,
                      icon: Clock,
                      color: "text-slate-600",
                      bgColor: "bg-slate-100",
                    },
                  ].map((schedule) => (
                    <div
                      key={schedule.period}
                      className="group cursor-pointer hover:shadow-md transition-all duration-300 p-4 border rounded-xl bg-white hover:bg-gradient-to-r hover:from-white hover:to-gray-50"
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`${schedule.bgColor} p-2 rounded-lg`}>
                          <schedule.icon
                            className={`h-4 w-4 ${schedule.color}`}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <p className="font-semibold text-gray-900">
                              {schedule.period}
                            </p>
                            <Badge
                              variant={
                                schedule.status === "confirmed"
                                  ? "default"
                                  : schedule.status === "likely"
                                  ? "secondary"
                                  : "outline"
                              }
                              className="text-xs"
                            >
                              {schedule.confidence}%
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            {schedule.granite}
                          </p>

                          <div className="space-y-1">
                            <div className="flex justify-between text-xs">
                              <span className="text-gray-500">
                                Orders: {schedule.orders}
                              </span>
                              <span className="font-medium">
                                {formatZAR(schedule.amount)}
                              </span>
                            </div>
                            <div className="bg-gray-200 rounded-full h-1.5">
                              <div
                                className={`h-1.5 rounded-full transition-all duration-1000 ${
                                  schedule.status === "confirmed"
                                    ? "bg-emerald-400"
                                    : schedule.status === "likely"
                                    ? "bg-blue-400"
                                    : "bg-amber-400"
                                }`}
                                style={{ width: `${schedule.confidence}%` }}
                              />
                            </div>
                            <p className="text-xs text-gray-500">
                              {schedule.completion}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Pipeline Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Pipeline Summary & Insights</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">50</div>
                  <p className="text-sm text-muted-foreground">Total Orders</p>
                  <p className="text-xs text-blue-600">Across all stages</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {formatZAR(2400000)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Total Pipeline Value
                  </p>
                  <p className="text-xs text-green-600">Expected revenue</p>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">6</div>
                  <p className="text-sm text-muted-foreground">
                    Ready to Manufacture
                  </p>
                  <p className="text-xs text-orange-600">
                    100% payment complete
                  </p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">68%</div>
                  <p className="text-sm text-muted-foreground">
                    Avg. Completion
                  </p>
                  <p className="text-xs text-purple-600">Across all orders</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Materials & Pricing Tab */}
        <TabsContent value="materials" className="space-y-6">
          {/* Materials Overview Cards */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border border-emerald-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-emerald-700 text-sm font-medium">
                      Total Savings
                    </p>
                    <p className="text-3xl font-bold text-emerald-900">
                      {formatZAR(5439)}
                    </p>
                    <p className="text-emerald-600 text-xs flex items-center">
                      <ArrowUpRight className="h-3 w-3 mr-1" />
                      From price locks
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-emerald-100 rounded-full flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-emerald-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-700 text-sm font-medium">
                      Price Protected
                    </p>
                    <p className="text-3xl font-bold text-blue-900">78%</p>
                    <p className="text-blue-600 text-xs">Of total volume</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Target className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-rose-50 to-rose-100 border border-rose-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-rose-700 text-sm font-medium">
                      Critical Stock
                    </p>
                    <p className="text-3xl font-bold text-rose-900">2</p>
                    <p className="text-rose-600 text-xs">Need reordering</p>
                  </div>
                  <div className="h-12 w-12 bg-rose-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="h-6 w-6 text-rose-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-700 text-sm font-medium">
                      Price Trend
                    </p>
                    <p className="text-3xl font-bold text-slate-900">+6.2%</p>
                    <p className="text-slate-600 text-xs flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      Last 30 days
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-slate-100 rounded-full flex items-center justify-center">
                    <LineChart className="h-6 w-6 text-slate-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 lg:grid-cols-2">
            {/* Material Requirements Forecast */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5" />
                  <span>Material Requirements Forecast</span>
                </CardTitle>
                <CardDescription>
                  Projected material needs based on order pipeline
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      material: "Premium Black Granite",
                      thisMonth: 45.5,
                      nextMonth: 62.3,
                      unit: "m²",
                      trend: "up",
                      stockLevel: 85.2,
                      reorderPoint: 50,
                      status: "adequate",
                    },
                    {
                      material: "Grey Granite",
                      thisMonth: 38.2,
                      nextMonth: 41.8,
                      unit: "m²",
                      trend: "up",
                      stockLevel: 42.1,
                      reorderPoint: 40,
                      status: "low",
                    },
                    {
                      material: "Engraving Tools",
                      thisMonth: 12,
                      nextMonth: 15,
                      unit: "sets",
                      trend: "up",
                      stockLevel: 8,
                      reorderPoint: 10,
                      status: "critical",
                    },
                    {
                      material: "Polish Compound",
                      thisMonth: 28.5,
                      nextMonth: 35.2,
                      unit: "kg",
                      trend: "up",
                      stockLevel: 65.8,
                      reorderPoint: 25,
                      status: "adequate",
                    },
                  ].map((material) => (
                    <div
                      key={material.material}
                      className="cursor-pointer hover:bg-accent/50 transition-colors p-4 border rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <p className="font-medium">{material.material}</p>
                          <Badge
                            variant={
                              material.status === "critical"
                                ? "destructive"
                                : material.status === "low"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {material.status}
                          </Badge>
                        </div>
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">This Month</p>
                          <p className="font-medium">
                            {material.thisMonth} {material.unit}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Next Month</p>
                          <p className="font-medium">
                            {material.nextMonth} {material.unit}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Current Stock</p>
                          <p className="font-medium">
                            {material.stockLevel} {material.unit}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Granite Price Tracking */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <LineChart className="h-5 w-5" />
                  <span>Granite Price Tracking & Commitments</span>
                </CardTitle>
                <CardDescription>
                  Real-time pricing with historical trends and price locks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      type: "Premium Black Granite",
                      current: 450,
                      locked: 425,
                      trend: "+5.9%",
                      status: "locked",
                      volume: 125.5,
                      savings: 3138,
                      expiry: "Mar 15",
                    },
                    {
                      type: "Grey Granite",
                      current: 380,
                      locked: 365,
                      trend: "+4.1%",
                      status: "locked",
                      volume: 89.2,
                      savings: 1338,
                      expiry: "Feb 28",
                    },
                    {
                      type: "White Granite",
                      current: 520,
                      locked: null,
                      trend: "+8.3%",
                      status: "market",
                      volume: 45.8,
                      savings: 0,
                      expiry: null,
                    },
                    {
                      type: "Custom Colors",
                      current: 680,
                      locked: 650,
                      trend: "+4.6%",
                      status: "partial",
                      volume: 32.1,
                      savings: 963,
                      expiry: "Apr 10",
                    },
                  ].map((granite) => (
                    <div
                      key={granite.type}
                      className="cursor-pointer hover:bg-accent/50 transition-colors p-4 border rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <p className="font-medium">{granite.type}</p>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              granite.status === "locked"
                                ? "default"
                                : granite.status === "partial"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {granite.status === "locked"
                              ? "Price Locked"
                              : granite.status === "partial"
                              ? "Partial Lock"
                              : "Market Rate"}
                          </Badge>
                          {granite.expiry && (
                            <Badge variant="outline" className="text-xs">
                              Expires {granite.expiry}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Current Price</p>
                          <p className="font-medium">
                            {formatZAR(granite.current)}/m²
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Locked Price</p>
                          <p className="font-medium">
                            {granite.locked
                              ? `${formatZAR(granite.locked)}/m²`
                              : "N/A"}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">30-Day Trend</p>
                          <p className="font-medium text-green-600">
                            {granite.trend}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Savings</p>
                          <p className="font-medium text-blue-600">
                            {granite.savings > 0
                              ? formatZAR(granite.savings)
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Materials Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Materials Cost Summary</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {formatZAR(5439)}
                  </div>
                  <p className="text-sm text-muted-foreground">Total Savings</p>
                  <p className="text-xs text-green-600">From price locks</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">78%</div>
                  <p className="text-sm text-muted-foreground">
                    Price Protected
                  </p>
                  <p className="text-xs text-blue-600">Of total volume</p>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">2</div>
                  <p className="text-sm text-muted-foreground">
                    Critical Stock
                  </p>
                  <p className="text-xs text-orange-600">Need reordering</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    +6.2%
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Avg. Price Trend
                  </p>
                  <p className="text-xs text-purple-600">Last 30 days</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Production Planning Tab */}
        <TabsContent value="production" className="space-y-8">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Factory className="h-5 w-5" />
                  <span>Production Planning Tools</span>
                </CardTitle>
                <CardDescription>
                  Upcoming manufacturing batches and optimization suggestions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Manufacturing Batches */}
                  <div>
                    <h4 className="font-medium mb-3">
                      Upcoming Manufacturing Batches
                    </h4>
                    <div className="space-y-3">
                      {[
                        {
                          type: "Black Granite",
                          orders: 8,
                          efficiency: 92,
                          date: "Feb 5-12",
                        },
                        {
                          type: "Grey Granite",
                          orders: 12,
                          efficiency: 88,
                          date: "Feb 12-19",
                        },
                        {
                          type: "Mixed Premium",
                          orders: 6,
                          efficiency: 85,
                          date: "Feb 19-26",
                        },
                      ].map((batch) => (
                        <div key={batch.type} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-medium">{batch.type}</p>
                            <Badge variant="outline">{batch.date}</Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">Orders</p>
                              <p className="font-medium">
                                {batch.orders} units
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">
                                Efficiency
                              </p>
                              <p className="font-medium text-green-600">
                                {batch.efficiency}%
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Optimization Suggestions */}
                  <div>
                    <h4 className="font-medium mb-3">
                      Optimization Suggestions
                    </h4>
                    <div className="space-y-2">
                      {[
                        {
                          suggestion: "Batch similar granite types together",
                          impact: "15% efficiency gain",
                        },
                        {
                          suggestion:
                            "Schedule premium orders for experienced team",
                          impact: "Reduce rework by 8%",
                        },
                        {
                          suggestion: "Optimize cutting patterns",
                          impact: "12% material savings",
                        },
                      ].map((opt, index) => (
                        <div
                          key={index}
                          className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg"
                        >
                          <Target className="h-4 w-4 text-blue-500 mt-0.5" />
                          <div>
                            <p className="text-sm font-medium">
                              {opt.suggestion}
                            </p>
                            <p className="text-xs text-blue-600">
                              {opt.impact}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5" />
                  <span>Financial Projections</span>
                </CardTitle>
                <CardDescription>
                  Revenue forecasts and margin analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Revenue Projections */}
                  <div>
                    <h4 className="font-medium mb-3">Expected Revenue</h4>
                    <div className="space-y-3">
                      {[
                        {
                          period: "This Month",
                          amount: 864000,
                          confidence: "High",
                          orders: 18,
                        },
                        {
                          period: "Next Month",
                          amount: 1152000,
                          confidence: "Medium",
                          orders: 24,
                        },
                        {
                          period: "Quarter",
                          amount: 3456000,
                          confidence: "Medium",
                          orders: 72,
                        },
                      ].map((projection) => (
                        <div
                          key={projection.period}
                          className="p-3 border rounded-lg"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-medium">{projection.period}</p>
                            <Badge
                              variant={
                                projection.confidence === "High"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {projection.confidence} Confidence
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">Revenue</p>
                              <p className="font-medium text-green-600">
                                {formatZAR(projection.amount)}
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Orders</p>
                              <p className="font-medium">
                                {projection.orders} units
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Cost & Margin Analysis */}
                  <div>
                    <h4 className="font-medium mb-3">Cost & Margin Analysis</h4>
                    <div className="space-y-3">
                      <div className="p-3 border rounded-lg">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">
                              Material Costs (Locked)
                            </p>
                            <p className="font-medium">{formatZAR(345600)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">
                              Material Costs (Market)
                            </p>
                            <p className="font-medium">{formatZAR(172800)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">
                              Projected Margin
                            </p>
                            <p className="font-medium text-green-600">42.5%</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">
                              Price Protection
                            </p>
                            <p className="font-medium text-blue-600">
                              {formatZAR(28800)} saved
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Production Capacity Planning */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Truck className="h-5 w-5" />
                <span>Production Capacity Planning</span>
              </CardTitle>
              <CardDescription>
                Capacity utilization and forecasting based on payment completion
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-3">
                <div className="space-y-4">
                  <h4 className="font-medium">Current Capacity</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Manufacturing</span>
                        <span>78%</span>
                      </div>
                      <Progress value={78} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Team Utilization</span>
                        <span>85%</span>
                      </div>
                      <Progress value={85} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Installation</span>
                        <span>62%</span>
                      </div>
                      <Progress value={62} className="h-2" />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Forecasted Demand</h4>
                  <div className="space-y-3">
                    {[
                      { week: "Week 1", demand: 95, capacity: 100 },
                      { week: "Week 2", demand: 110, capacity: 100 },
                      { week: "Week 3", demand: 88, capacity: 100 },
                      { week: "Week 4", demand: 102, capacity: 100 },
                    ].map((forecast) => (
                      <div
                        key={forecast.week}
                        className="flex items-center justify-between text-sm"
                      >
                        <span>{forecast.week}</span>
                        <div className="flex items-center space-x-2">
                          <span
                            className={
                              forecast.demand > forecast.capacity
                                ? "text-red-600"
                                : "text-green-600"
                            }
                          >
                            {forecast.demand}%
                          </span>
                          <div className="w-16 h-2 bg-gray-200 rounded">
                            <div
                              className={`h-2 rounded ${
                                forecast.demand > forecast.capacity
                                  ? "bg-red-500"
                                  : "bg-green-500"
                              }`}
                              style={{
                                width: `${Math.min(forecast.demand, 100)}%`,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Recommendations</h4>
                  <div className="space-y-2">
                    <div className="p-2 bg-yellow-50 rounded text-sm">
                      <p className="font-medium text-yellow-800">
                        Capacity Alert
                      </p>
                      <p className="text-yellow-700">
                        Week 2 shows 110% demand
                      </p>
                    </div>
                    <div className="p-2 bg-blue-50 rounded text-sm">
                      <p className="font-medium text-blue-800">Suggestion</p>
                      <p className="text-blue-700">
                        Consider overtime or temp staff
                      </p>
                    </div>
                    <div className="p-2 bg-green-50 rounded text-sm">
                      <p className="font-medium text-green-800">Opportunity</p>
                      <p className="text-green-700">
                        Week 3 has spare capacity
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
