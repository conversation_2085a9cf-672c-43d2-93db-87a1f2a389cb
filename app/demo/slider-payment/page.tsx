"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { SliderPurchaseDrawer } from "@/components/payment/slider-purchase-drawer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { products } from "@/data/products";
import { formatPrice } from "@/lib/utils";
import { ShoppingCart, Sparkles, Zap, Users, Calculator } from "lucide-react";
import Image from "next/image";

export default function SliderPaymentDemo() {
  // Use the first product for demo
  const demoProduct = products[0];

  return (
    <MobileLayout showBackButton>
      <div className="space-y-6 pb-24">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold">New Payment Flow</h1>
            <Sparkles className="h-6 w-6 text-primary" />
          </div>
          <p className="text-muted-foreground">
            Experience our new slider-based payment selection
          </p>
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            ✨ Just Launched
          </Badge>
        </div>

        {/* Demo Product */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Demo Product
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="aspect-square relative rounded-lg overflow-hidden">
              <Image
                src={demoProduct.images[0] || "/images/placeholder.png"}
                alt={demoProduct.name}
                fill
                className="object-cover"
              />
            </div>
            <div>
              <h3 className="font-semibold text-lg">{demoProduct.name}</h3>
              <p className="text-muted-foreground text-sm mt-1">
                {demoProduct.description}
              </p>
              <div className="text-xl font-bold text-primary mt-2">
                {formatPrice(demoProduct.price)}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Features */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              What's New
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Calculator className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <div className="font-medium">Single Slider Decision</div>
                  <div className="text-sm text-muted-foreground">
                    Choose between solo and group purchase with one simple slider
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Users className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <div className="font-medium">Dynamic UI Adaptation</div>
                  <div className="text-sm text-muted-foreground">
                    Interface changes automatically based on your selection
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Sparkles className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <div className="font-medium">Seamless Experience</div>
                  <div className="text-sm text-muted-foreground">
                    No more tabs or complex navigation - just slide and go
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* How It Works */}
        <Card>
          <CardHeader>
            <CardTitle>How It Works</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                  1
                </div>
                <div className="text-sm">
                  <strong>Slide to choose:</strong> 1 person = solo, 2+ = group
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                  2
                </div>
                <div className="text-sm">
                  <strong>UI adapts:</strong> Payment options change automatically
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                  3
                </div>
                <div className="text-sm">
                  <strong>Configure & continue:</strong> Set terms and invite members
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Try It Button */}
        <div className="fixed bottom-16 left-0 right-0 z-40 bg-white border-t p-4">
          <div className="max-w-md mx-auto">
            <SliderPurchaseDrawer product={demoProduct} quantity={1}>
              <Button className="w-full" size="lg">
                <ShoppingCart className="mr-2 h-4 w-4" />
                Try New Payment Flow
              </Button>
            </SliderPurchaseDrawer>
          </div>
        </div>

        {/* Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>Before vs After</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="font-medium text-red-600">❌ Before</div>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>• Multiple tabs to navigate</div>
                  <div>• Complex decision tree</div>
                  <div>• Cognitive overhead</div>
                  <div>• Back-and-forth navigation</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="font-medium text-green-600">✅ After</div>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>• Single slider interaction</div>
                  <div>• Intuitive flow</div>
                  <div>• Reduced friction</div>
                  <div>• Seamless experience</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Details */}
        <Card>
          <CardHeader>
            <CardTitle>Technical Implementation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>Components:</strong> SliderPaymentFlow, SliderPurchaseDrawer
              </div>
              <div>
                <strong>State Management:</strong> React hooks with dynamic UI updates
              </div>
              <div>
                <strong>Styling:</strong> Custom CSS modules with smooth animations
              </div>
              <div>
                <strong>UX Patterns:</strong> Progressive disclosure, smart defaults
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MobileLayout>
  );
}
