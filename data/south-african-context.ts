/**
 * South African context data for the memorial stone business
 */

// South African cities and provinces
export const southAfricanLocations = {
  provinces: [
    "Western Cape",
    "Eastern Cape", 
    "Northern Cape",
    "Free State",
    "KwaZulu-Natal",
    "North West",
    "Gauteng",
    "Mpumalanga",
    "Limpopo"
  ],
  cities: [
    "Cape Town",
    "Johannesburg", 
    "Durban",
    "Pretoria",
    "Port Elizabeth",
    "Bloemfontein",
    "East London",
    "Pietermaritzburg",
    "Kimberley",
    "Polokwane",
    "Nelspruit",
    "Rustenburg",
    "George",
    "Stellenbosch",
    "Paarl"
  ],
  cemeteries: [
    "Stellenbosch Cemetery",
    "Paarl Cemetery", 
    "Durbanville Memorial Park",
    "Brackenfell Cemetery",
    "Bellville Cemetery",
    "Goodwood Cemetery",
    "Maitland Cemetery",
    "Plumstead Cemetery",
    "Wynberg Cemetery",
    "Constantia Cemetery",
    "Hout Bay Cemetery",
    "Sea Point Cemetery",
    "Green Point Cemetery",
    "Observatory Cemetery",
    "Rondebosch Cemetery"
  ]
};

// South African names (mix of English, Afrikaans, Zulu, Xhosa)
export const southAfricanNames = {
  firstNames: [
    "Thabo", "<PERSON>ph<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>",
    "Mandla", "<PERSON>cious", "<PERSON><PERSON><PERSON>", "Le<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"
  ],
  surnames: [
    "Mthembu", "Nkomo", "Dlamini", "Mokoena", "Van der Merwe", "Botha", "Pretorius", "Du Plessis",
    "Mahlangu", "Khumalo", "Ndlovu", "Zulu", "Steyn", "Fourie", "Venter", "Nel",
    "Mabaso", "Sithole", "Radebe", "Molefe", "Kruger", "Smit", "Jacobs", "De Wet",
    "Ngcobo", "Cele", "Shabalala", "Ntuli", "Van Wyk", "Olivier", "Potgieter", "Coetzee"
  ]
};

// South African business context
export const southAfricanBusiness = {
  currency: "ZAR",
  currencySymbol: "R",
  phoneFormat: "+27 XX XXX XXXX",
  postalCodeFormat: "XXXX",
  businessTypes: [
    "Memorial Stone Manufacturing",
    "Monument Crafting",
    "Granite Works",
    "Memorial Services",
    "Stone Masonry",
    "Cemetery Services"
  ],
  provinces: southAfricanLocations.provinces
};

// Generate South African phone numbers
export function generateSAPhoneNumber(): string {
  const areaCodes = ["11", "21", "31", "41", "51", "12", "13", "14", "15", "16", "17", "18"];
  const areaCode = areaCodes[Math.floor(Math.random() * areaCodes.length)];
  const number = Math.floor(Math.random() * 9000000) + 1000000;
  return `+27 ${areaCode} ${number.toString().slice(0, 3)} ${number.toString().slice(3)}`;
}

// Generate South African addresses
export function generateSAAddress(): string {
  const streetNumbers = Math.floor(Math.random() * 999) + 1;
  const streetNames = [
    "Church Street", "Main Road", "High Street", "Victoria Street", "Long Street",
    "Voortrekker Road", "Jan Smuts Avenue", "Nelson Mandela Boulevard", "Adderley Street",
    "Loop Street", "Strand Street", "Kloof Street", "Orange Street", "Bree Street"
  ];
  const suburbs = [
    "Gardens", "Tamboerskloof", "Oranjezicht", "Higgovale", "Vredehoek",
    "Woodstock", "Observatory", "Rondebosch", "Claremont", "Newlands",
    "Constantia", "Hout Bay", "Camps Bay", "Sea Point", "Green Point"
  ];
  
  const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
  const suburb = suburbs[Math.floor(Math.random() * suburbs.length)];
  const city = southAfricanLocations.cities[Math.floor(Math.random() * southAfricanLocations.cities.length)];
  const postalCode = Math.floor(Math.random() * 9000) + 1000;
  
  return `${streetNumbers} ${streetName}, ${suburb}, ${city} ${postalCode}`;
}

// Generate South African names
export function generateSAName(): { firstName: string; lastName: string; fullName: string } {
  const firstName = southAfricanNames.firstNames[Math.floor(Math.random() * southAfricanNames.firstNames.length)];
  const lastName = southAfricanNames.surnames[Math.floor(Math.random() * southAfricanNames.surnames.length)];
  return {
    firstName,
    lastName,
    fullName: `${firstName} ${lastName}`
  };
}

// Generate South African cemetery address
export function generateCemeteryAddress(): string {
  const cemetery = southAfricanLocations.cemeteries[Math.floor(Math.random() * southAfricanLocations.cemeteries.length)];
  const sections = ["Section A", "Section B", "Section C", "Garden Section", "Memorial Garden", "Rose Garden"];
  const section = sections[Math.floor(Math.random() * sections.length)];
  const plotNumber = Math.floor(Math.random() * 200) + 1;
  const city = southAfricanLocations.cities[Math.floor(Math.random() * southAfricanLocations.cities.length)];
  
  return `${cemetery}, ${section}, Plot ${plotNumber}, ${city}`;
}

// Convert USD prices to ZAR (approximate rate: 1 USD = 18.5 ZAR)
export function convertToZAR(usdPrice: number): number {
  return Math.round(usdPrice * 18.5);
}

// Format ZAR currency
export function formatZAR(amount: number): string {
  return `R${amount.toLocaleString('en-ZA')}`;
}

// South African business registration numbers
export function generateBusinessRegistration(): string {
  const year = Math.floor(Math.random() * 24) + 2000;
  const number = Math.floor(Math.random() * 999999) + 100000;
  return `${year}/${number}/07`;
}

// South African tax numbers
export function generateTaxNumber(): string {
  const number = Math.floor(Math.random() * 9000000000) + 1000000000;
  return number.toString();
}
